import os
import asyncio
import logging
import aiohttp
import json
from dotenv import load_dotenv
from livekit import rtc

from livekit.agents import Agent, AgentSession, JobContext, WorkerOptions, cli, llm
from livekit.agents.stt import SpeechEventType
from livekit.agents.utils import http_context
from livekit.plugins import deepgram, cartesia, openai, silero

# Load environment variables from .env/.env.local if present
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


CONVEX_SITE_URL = os.environ.get("CONVEX_SITE_URL")
DEBUG_AGENT_LOG = os.environ.get("DEBUG_AGENT_LOG") == "1"
DEBUG_AGENT_TRANSCRIPTS = os.environ.get("DEBUG_AGENT_TRANSCRIPTS") == "1"

class SessionAuth:
    def __init__(self) -> None:
        self.token: str | None = None
        self.email: str | None = None
        self.name: str | None = None


class WiggleWalkAgent(Agent):
    def __init__(self, name: str, session_auth: SessionAuth | None = None, room: rtc.Room | None = None) -> None:
        name_hint = (session_auth.name if session_auth and session_auth.name else "")
        personalized = (f" Address the user as {name_hint} when appropriate." if name_hint else "")
        super().__init__(
            name=name,
            instructions=(
                "You are a friendly and helpful AI assistant for WiggleWalk, a dog walking service. "
                "Keep your answers concise and cheerful." + personalized
            )
        )
        self._session_auth = session_auth
        self._room = room

    async def process_chat(self, chat_history: llm.ChatContext):
        last = chat_history.messages[-1] if chat_history.messages else None
        user_input = last.content if last else ""
        # Handle initial greet when there's no user input yet
        if not user_input or not isinstance(user_input, str):
            try:
                # Wait briefly for auth to arrive so we can personalize
                for _ in range(20):
                    if getattr(getattr(self, "_session_auth", None), "name", None):
                        break
                    await asyncio.sleep(0.05)
                name_hint = getattr(getattr(self, "_session_auth", None), "name", None)
                reply_text = (
                    f"Hi {name_hint}, I’m your WiggleWalk assistant. How can I help today?"
                    if name_hint
                    else "Hi there, I’m your WiggleWalk assistant. How can I help today?"
                )
                try:
                    if self._room and getattr(self._room, "local_participant", None):
                        payload = json.dumps({"type": "agent_text", "text": str(reply_text)}).encode("utf-8")
                        self._room.local_participant.publish_data(payload, reliable=True)  # type: ignore[attr-defined]
                except Exception:
                    pass
                yield llm.ChatStream(text=reply_text)
            except Exception:
                pass
            return

        if DEBUG_AGENT_LOG:
            logger.info("[agent] process_chat invoked; messages=%d", len(chat_history.messages or []))

        try:
            # Ensure we attach auth before calling backend; wait briefly if token not yet received
            if not getattr(getattr(self, "_session_auth", None), "token", None):
                for _ in range(20):
                    if getattr(getattr(self, "_session_auth", None), "token", None):
                        break
                    await asyncio.sleep(0.05)
            reply_text = await self._call_convex(user_input)
        except Exception as e:
            logger.error("Failed to process chat via Convex API", exc_info=e)
            reply_text = (
                "I'm sorry, I'm having trouble connecting right now. Please try again in a moment."
            )

        chat_history.messages.append(
            llm.ChatMessage(role=llm.ChatRole.ASSISTANT, content=reply_text)
        )
        if DEBUG_AGENT_LOG:
            logger.info("[agent] assistant reply enqueued; length=%d", len(reply_text or ""))
        # Publish agent text to room for frontend conversation log
        try:
            if self._room and getattr(self._room, "local_participant", None):
                payload = json.dumps({"type": "agent_text", "text": str(reply_text or "")}).encode("utf-8")
                # best-effort publish; ignore failures
                self._room.local_participant.publish_data(payload, reliable=True)  # type: ignore[attr-defined]
        except Exception:
            pass
        yield llm.ChatStream(text=reply_text)

    async def _call_convex(self, text: str) -> str:
        if not CONVEX_SITE_URL:
            return "I can't reach the backend yet. Please configure CONVEX_SITE_URL."

        # Route to the stateful Convex agent HTTP endpoint which calls dogWalkerActions.talk
        endpoint = f"{CONVEX_SITE_URL}/api/voice/talk"
        http = http_context.http_session()

        headers = {"content-type": "application/json"}
        tok = getattr(getattr(self, "_session_auth", None), "token", None)
        if tok:
            headers["Authorization"] = f"Bearer {tok}"
        else:
            logger.warning("[agent] No auth token found for Convex request; proceeding unauthenticated")

        try:
            async with http.post(
                endpoint,
                headers=headers,
                json={"input": text},
                timeout=20,
            ) as resp:
                if not resp.ok:
                    error_text = await resp.text()
                    logger.error("HTTP error calling Convex API: %s - %s", resp.status, error_text)
                    return "I'm having trouble connecting right now. Please try again shortly."

                payload = await resp.json()
                # dogWalkerActions.talk returns { text, threadId }
                if isinstance(payload, dict):
                    reply_text = payload.get("text")
                    if isinstance(reply_text, str) and reply_text:
                        return reply_text
                logger.warning("[agent] Unexpected Convex payload: %s", payload)
                return "I received something I couldn't understand. Could you try rephrasing?"
        except asyncio.TimeoutError:
            logger.error("Timeout error calling Convex API")
            return "My connection timed out. Could you please repeat that?"
        except aiohttp.ClientError as e:
            logger.error("HTTP error calling Convex API: %s", e)
            return "I'm experiencing network issues. Please try again."
        except Exception as e:
            logger.error("Unexpected error in _call_convex", exc_info=e)
            return "An unexpected error occurred. Please try again."

    def _format_convex_response(self, payload: dict) -> str:
        ui = payload.get("ui")
        data = payload.get("data")
        try:
            if ui == "answer_card" and isinstance(data, dict) and data.get("answer"):
                return str(data["answer"]).strip()
            if ui == "options" and isinstance(data, list):
                total = len(data)
                if total == 0:
                    return (
                        "I couldn't find any available times. Would you like to try another day or time?"
                    )
                picks = data[:3]
                phrase = ", ".join([f"{o['date']} at {o['time']}" for o in picks if 'date' in o and 'time' in o])
                return f"I found {total} options. For example: {phrase}. Which one works for you?"
            if ui == "booking_preview" and isinstance(data, dict):
                mins = f"{data.get('durationMins')}-minute" if data.get("durationMins") else "a"
                dogs = ", ".join(data.get("dogs", []) or ["your dog"])
                when = f" on {', '.join(data.get('dates', []))}" if data.get("dates") else ""
                return f"You want a {mins} walk for {dogs}{when}. Should I check availability?"
            if ui == "booking_confirm" and isinstance(data, dict):
                total = f"${data.get('total')}" if isinstance(data.get("total"), (int, float)) else "the shown total"
                return f"Great, the total is {total}. Would you like me to finalize the booking now?"
            if ui == "error" and isinstance(data, dict) and data.get("message"):
                return f"Sorry, {str(data['message'])}. Could you rephrase or provide a bit more detail?"
            if isinstance(payload, str):
                return payload
            if isinstance(data, dict) and data.get("answer"):
                return str(data["answer"])
        except Exception as e:
            logger.error("[agent] error formatting Convex response payload", exc_info=e)
            return "I had an issue understanding the response, please try again."
        if DEBUG_AGENT_LOG:
            logger.warning("[agent] unhandled Convex payload format; keys=%s", list(payload.keys()))
        return "I had an issue, please try again."


async def agent_entrypoint(ctx: JobContext):
    required_env = [
        "LIVEKIT_URL",
        "LIVEKIT_API_KEY",
        "LIVEKIT_API_SECRET",
        "DEEPGRAM_API_KEY",

        "OPENAI_API_KEY",
    ]
    missing = [k for k in required_env if not os.getenv(k)]
    if missing:
        raise RuntimeError(f"Missing environment variables: {', '.join(missing)}")

    session = AgentSession(
        stt=deepgram.STT(),
        llm=openai.LLM(),
        tts=cartesia.TTS(model="sonic-english"),
        vad=silero.VAD.load(),
    )
    auth = SessionAuth()
    # STT stream and wiring: pipe user audio to STT and push transcripts into chat context
    stt_stream = session.stt.stream()

    @session.on("track_subscribed")
    def on_track_subscribed(
        track: rtc.Track,
        publication: rtc.TrackPublication,
        participant: rtc.RemoteParticipant,
    ) -> None:  # type: ignore[no-redef]
        try:
            if track.kind == rtc.TrackKind.AUDIO and not participant.is_agent:
                if DEBUG_AGENT_LOG:
                    logger.info("[agent] audio track subscribed from participant=%s; wiring VAD→STT", getattr(participant, "identity", "unknown"))
                # activity heartbeat on new audio source
                try:
                    nonlocal last_activity
                    last_activity = asyncio.get_event_loop().time()
                except Exception:
                    pass
                session.vad.stream(track).pipe(stt_stream)
        except Exception as e:  # pragma: no cover
            logger.error("Error in track_subscribed handler", exc_info=e)

    async def stt_processor():
        async for ev in stt_stream:
            try:
                # heartbeat on any STT activity
                try:
                    nonlocal last_activity
                    last_activity = asyncio.get_event_loop().time()
                except Exception:
                    pass
                if getattr(ev, "type", None) == SpeechEventType.FINAL_TRANSCRIPT:
                    alts = getattr(ev, "alternatives", []) or []
                    transcript = (alts[0].transcript if alts else "").strip()
                    if not transcript:
                        continue
                    if DEBUG_AGENT_TRANSCRIPTS:
                        logger.info("[agent] FINAL transcript: %s", transcript[:160])
                    elif DEBUG_AGENT_LOG:
                        logger.info("[agent] FINAL transcript received; length=%d", len(transcript))
                    # Mirror FINAL user utterance to UI log
                    try:
                        if getattr(ctx, "room", None) and getattr(ctx.room, "local_participant", None):
                            payload = json.dumps({"type": "user_utterance", "text": transcript}).encode("utf-8")
                            ctx.room.local_participant.publish_data(payload, reliable=True)  # type: ignore[attr-defined]
                    except Exception:
                        pass
                    session.chat_context().messages.append(
                        llm.ChatMessage(role=llm.ChatRole.USER, content=transcript)
                    )
                    # Auto-end on explicit goodbye phrases
                    lowered = transcript.lower()
                    if any(kw in lowered for kw in ["goodbye", "bye", "end session", "end the session", "disconnect"]):
                        if DEBUG_AGENT_LOG:
                            logger.info("[agent] goodbye phrase detected; initiating shutdown")
                        try:
                            await session.generate_reply(instructions="Okay! I’ll end the session now. Talk soon!")
                        except Exception:
                            pass
                        try:
                            stt_task.cancel()
                        except Exception:
                            pass
                        asyncio.create_task(ctx.room.disconnect())
                        break
                    # Generate a reply for normal user utterances
                    try:
                        await session.generate_reply()
                    except Exception as e:
                        logger.error("generate_reply failed", exc_info=e)
            except asyncio.CancelledError:
                break
            except Exception as e:  # pragma: no cover
                logger.error("stt stream loop error", exc_info=e)

    stt_task = asyncio.create_task(stt_processor())

    # Capture auth info from the frontend once
    @session.on("data_received")
    def _on_data_received(data: bytes, participant: rtc.RemoteParticipant) -> None:  # type: ignore[name-defined]
        try:
            payload = json.loads(data.decode("utf-8")) if isinstance(data, (bytes, bytearray)) else None
            if isinstance(payload, dict) and payload.get("type") == "auth":
                auth.token = str(payload.get("token") or "") or None
                auth.email = str(payload.get("email") or "") or None
                auth.name = str(payload.get("name") or "") or None
                if DEBUG_AGENT_LOG:
                    logger.info("[agent] received auth; email=%s has_token=%s", auth.email or "", bool(auth.token))
        except Exception as e:
            if DEBUG_AGENT_LOG:
                logger.warning("[agent] failed to parse data_received: %s", str(e))

    logger.info(
        "Agent job received for room: %s",
        getattr(getattr(ctx, "room", None), "name", "unknown"),
    )

    if DEBUG_AGENT_LOG:
        logger.info("[agent] starting session for room=%s", getattr(ctx.room, "name", "unknown"))
    await session.start(room=ctx.room, agent=WiggleWalkAgent(name="WiggleWalk Assistant", session_auth=auth, room=ctx.room))
    await session.generate_reply(instructions="Greet the user briefly and ask how you can help.")
    # heartbeat on agent speak
    try:
        last_activity = asyncio.get_event_loop().time()
    except Exception:
        pass

    # Clean shutdown when last human leaves: stop STT and disconnect room
    @ctx.room.on("participant_disconnected")
    def _on_participant_disconnected(participant: rtc.RemoteParticipant) -> None:  # type: ignore[name-defined]
        try:
            humans = [p for p in getattr(ctx.room, 'remote_participants', {}).values() if not getattr(p, 'is_agent', False)]
            if len(humans) == 0:
                logger.info("[agent] last human left; disconnecting")
                try:
                    stt_stream.close()  # type: ignore[attr-defined]
                except Exception:
                    pass
                try:
                    stt_task.cancel()
                except Exception:
                    pass
                asyncio.create_task(ctx.room.disconnect())
        except Exception as e:
            logger.error("participant_disconnected handler error", exc_info=e)

    # Also stop STT if room disconnects for any reason
    @ctx.room.on("disconnected")
    def _on_room_disconnected() -> None:
        try:
            stt_task.cancel()
        except Exception:
            pass

    # Idle auto-end: disconnect after inactivity window (env override)
    idle_timeout_s = int(os.environ.get("AGENT_IDLE_TIMEOUT_S", "180"))
    last_activity = asyncio.get_event_loop().time()
    if DEBUG_AGENT_LOG:
        logger.info("[agent] idle watchdog started; timeout_s=%d", idle_timeout_s)

    async def idle_watchdog():
        nonlocal last_activity
        try:
            while True:
                await asyncio.sleep(10)
                now = asyncio.get_event_loop().time()
                if now - last_activity > idle_timeout_s:
                    if DEBUG_AGENT_LOG:
                        logger.info("[agent] idle watchdog fired; inactive_for_s=%.1f", now - last_activity)
                    try:
                        await session.generate_reply(instructions="I’ll hop off for now. Call me anytime!")
                    except Exception:
                        pass
                    try:
                        stt_task.cancel()
                    except Exception:
                        pass
                    asyncio.create_task(ctx.room.disconnect())
                    break
        except asyncio.CancelledError:
            pass

    watchdog_task = asyncio.create_task(idle_watchdog())

    # Wait for STT task to finish (cancelled on shutdown)
    try:
        await stt_task
    except asyncio.CancelledError:
        pass


if __name__ == "__main__":
    load_threshold = float(os.getenv("LIVEKIT_WORKER_LOAD_THRESHOLD", "0.95"))
    port = int(os.getenv("LIVEKIT_WORKER_PORT", "8081"))
    cli.run_app(WorkerOptions(entrypoint_fnc=agent_entrypoint, load_threshold=load_threshold, port=port))
