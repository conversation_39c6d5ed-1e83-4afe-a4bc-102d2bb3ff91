"use node";
// convex/agents/dogWalkerAgent.ts
import { z } from "zod";
import { createTool, Agent } from "@convex-dev/agent";
import { api, internal } from "../_generated/api";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore - runtime file provided by Convex
import { components } from "../_generated/api.js";
// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-ignore - provider lib is optional at build time
import { openai } from "@ai-sdk/openai";
import type { Doc, Id } from "../_generated/dataModel";
import type { Source } from "../rag";

type SlotAvailabilityResult = {
  available: boolean;
  reason: string;
  currentBookings: number;
  maxCapacity: number;
  remainingCapacity?: number;
};

export const checkAvailability = createTool({
  description: "Check whether a time slot is available.",
  args: z.object({
    startTime: z.string().describe("ISO start time"),
    durationMins: z.number().int().positive().max(180),
  }),
  handler: async (ctx, { startTime, durationMins }): Promise<SlotAvailabilityResult> => {
    return (await ctx.runQuery(api.booking.checkSlotAvailability, { startTime, durationMins })) as SlotAvailabilityResult;
  },
});

export const createBooking = createTool({
  description: "Create a dog walking booking.",
  args: z.object({
    startTime: z.string().describe("ISO start time"),
    durationMins: z.number().int().positive().max(180),
    dogName: z.string().min(1),
    dogBreed: z.string().optional(),
    dogNotes: z.string().optional(),
  }),
  handler: async (
    ctx,
    args: { startTime: string; durationMins: number; dogName: string; dogBreed?: string; dogNotes?: string }
  ): Promise<{ ok: true; paymentUrl: string }> => {
    const url = (await ctx.runAction(api.booking.bookWalkAuth, args)) as string;
    return { ok: true as const, paymentUrl: url };
  },
});

export const searchKnowledgeBase = createTool({
  description: "Answer policy or service questions from the knowledge base.",
  args: z.object({ query: z.string() }),
  handler: async (
    ctx,
    { query }
  ): Promise<{ answer: string; sources: Source[] }> => {
    try {
      console.log("[tool] searchKnowledgeBase", { queryLength: (query || "").length });
    } catch {
      /* ignore */
    }
    return (await ctx.runAction(internal.voiceTools.answerQuestion, { query })) as {
      answer: string;
      sources: Source[];
    };
  },
});

export const listMyDogs = createTool({
  description: "List the current user's registered dogs.",
  args: z.object({}),
  handler: async (
    ctx
  ): Promise<{ dogs: Array<{ name: string; breed?: string }> }> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity?.email) return { dogs: [] };
    const owner = await ctx.runQuery(api.owners.getByEmail, { email: identity.email });
    if (!owner) return { dogs: [] };
    try {
      console.log("[tool] listMyDogs", { email: identity.email, ownerId: owner._id });
    } catch {
      /* ignore */
    }
    const dogs = (await ctx.runQuery(api.dogs.getByOwner, { ownerId: owner._id })) as Doc<"dogs">[];
    const list = Array.isArray(dogs) ? dogs : [];
    return { dogs: list.map((d) => ({ name: d.name, breed: d.breed })) };
  },
});

// Structured intake update tool
type UpdateIntakeResult = { ok: true; message: string; ownerId: Id<"owners">; dogId?: Id<"dogs"> };

export const updateIntakeInformation = createTool({
  description:
    "Update or create an intake form for the authenticated user. Use for dog's name, breed, temperament, health needs, and prerequisites.",
  args: z.object({
    dogName: z.string().min(1),
    dogBreed: z.string().optional(),
    temperament: z.array(z.string()).optional(),
    healthNeeds: z.string().optional(),
    vaccines: z.boolean().optional(),
    license: z.boolean().optional(),
    friendly: z.boolean().optional(),
    notes: z.string().optional(),
  }),
  handler: async (ctx, args): Promise<UpdateIntakeResult> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity?.email) throw new Error("User must be authenticated to update intake.");
    try {
      console.log("[tool] updateIntakeInformation", { email: identity.email, dogName: args.dogName });
    } catch {
      /* ignore */
    }

    const res = (await ctx.runMutation(api.intakes.upsertFromAgent, {
      ownerEmail: identity.email,
      ownerName: identity.name || "User",
      dog: { name: args.dogName, breed: args.dogBreed },
      questionnaire: { temperament: args.temperament, healthNeeds: args.healthNeeds },
      prerequisites: {
        vaccines: args.vaccines ?? false,
        license: args.license ?? false,
        friendly: args.friendly ?? false,
      },
      notes: args.notes,
    })) as { ownerId: Id<"owners">; dogId?: Id<"dogs"> };

    return {
      ok: true as const,
      message: `Saved intake for ${args.dogName}`,
      ownerId: res.ownerId,
      dogId: res.dogId,
    };
  },
});

export const dogWalker: Agent = new Agent(components.agent, {
  name: "WiggleWalk Assistant",
  instructions:
    [
      "You are a friendly and highly capable AI assistant for WiggleWalk, a dog walking service.",
      "Goals:",
      "1) Help new users complete their intake (dog name, breed, temperament, special needs, prerequisites).",
      "2) Help existing users book dog walks (check availability, then create booking).",
      "3) Answer short questions about policies/services using the knowledge base.",
      "\nCRITICAL:",
      "- You will receive a [USER CONTEXT] block inside the prompt. ALWAYS read it first and avoid asking for info that already exists.",
      "- Use tools proactively: updateIntakeInformation, checkAvailability, createBooking, listMyDogs, searchKnowledgeBase.",
      "- Keep responses concise and cheerful. Confirm before saving or booking.",
    ].join("\n"),
  chat: openai.chat(process.env.AI_MODEL || "gpt-4o-mini"),
  textEmbedding: openai.embedding(process.env.AI_EMBEDDING_MODEL || "text-embedding-3-small"),
  tools: { checkAvailability, createBooking, searchKnowledgeBase, listMyDogs, updateIntakeInformation },
  // Allow multi-step tool use in a single request
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore - maxSteps is supported by the runtime Agent implementation
  maxSteps: 5,
});


