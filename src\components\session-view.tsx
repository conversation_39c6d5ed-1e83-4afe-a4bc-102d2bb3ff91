import { useEffect, useState, useRef, useCallback } from 'react'
import { useAuth, useUser } from '@clerk/clerk-react'
import type { AppConfig } from '@/lib/types'
import { useRoomContext } from '@livekit/components-react'
import { createLocalTracks, RoomEvent } from 'livekit-client'

type ConversationMessage = {
  id: string
  role: 'user' | 'assistant'
  content: string
  timestamp: Date
}

type SessionViewProps = {
  appConfig: AppConfig
  sessionStarted: boolean
  onDisconnect?: () => void
}

export function SessionView({ appConfig, onDisconnect }: SessionViewProps) {
  const room = useRoomContext()
  const [conversation, setConversation] = useState<ConversationMessage[]>([])
  const messagesEndRef = useRef<HTMLDivElement>(null)
  const [micActive, setMicActive] = useState(false)
  const micTracksRef = useRef<ReturnType<typeof createLocalTracks> extends Promise<infer T> ? T : unknown>([] as unknown)
  const { getToken } = useAuth()
  const { user } = useUser()

  // Mic lifecycle: publish when micActive, unpublish/stop when turned off
  useEffect(() => {
    let cancelled = false
    async function enableMic() {
      try {
        // only when room is connected
        const isConnected = (room as unknown as { state?: string })?.state?.toLowerCase?.() === 'connected'
        if (!isConnected) return
        // avoid duplicate mic publication
        try {
          const lp = room.localParticipant as unknown as { trackPublications?: Map<unknown, { source?: string; track?: unknown }> }
          const pubs = Array.from(lp?.trackPublications?.values?.() || [])
          const hasMic = pubs.some((p) => (p?.source || '').toString().toLowerCase() === 'microphone')
          if (hasMic) return
        } catch {
          // ignore track publication inspection errors
        }
        const tracks = await createLocalTracks({ audio: true })
        if (cancelled) return
        micTracksRef.current = tracks as unknown
        for (const t of tracks) {
          await room.localParticipant.publishTrack(t)
        }
      } catch (e) {
        console.warn('[session-view] mic enable failed', (e as Error)?.message || e)
        setMicActive(false)
      }
    }
    async function disableMic() {
      try {
        const lp = room.localParticipant as unknown as { trackPublications?: Map<unknown, { track?: { stop?: () => void } | null }> } & { unpublishTrack: (track: unknown, opts?: { stopLocalTrack?: boolean }) => unknown }
        const pubs = Array.from(lp?.trackPublications?.values?.() || [])
        for (const pub of pubs) {
          try { pub?.track?.stop?.() } catch (e) { console.warn('[session-view] stop track failed', e) }
          try { const track = (pub as unknown as { track?: unknown }).track; if (track) void lp.unpublishTrack(track, { stopLocalTrack: true }) } catch (e) { console.warn('[session-view] unpublish track failed', e) }
        }
      } catch (e) {
        console.warn('[session-view] disable mic error', e)
      } finally {
        micTracksRef.current = [] as unknown
      }
    }
    if (micActive) { void enableMic() } else { void disableMic() }
    return () => { cancelled = true }
  }, [micActive, room])

  // Ensure mic off on unmount and room disconnect
  useEffect(() => {
    const onDisconnected = () => setMicActive(false)
    room.on(RoomEvent.Disconnected, onDisconnected)
    return () => {
      room.off(RoomEvent.Disconnected, onDisconnected)
      setMicActive(false)
    }
  }, [room])

  // Listen for data messages to capture conversation
  useEffect(() => {
    const onDataReceived = (payload: Uint8Array) => {
      try {
        const txt = new TextDecoder().decode(payload)
        const msg = JSON.parse(txt)
        
        if (msg?.type === 'user_utterance' && msg?.text) {
          // Add user message to conversation
          const userMessage: ConversationMessage = {
            id: `user-${Date.now()}-${Math.random()}`,
            role: 'user',
            content: msg.text,
            timestamp: new Date()
          }
          setConversation(prev => [...prev, userMessage])
        } else if (msg?.type === 'agent_text' && msg?.text) {
          // Add assistant message to conversation
          const assistantMessage: ConversationMessage = {
            id: `assistant-${Date.now()}-${Math.random()}`,
            role: 'assistant',
            content: msg.text,
            timestamp: new Date()
          }
          setConversation(prev => [...prev, assistantMessage])
        }
      } catch (e) {
        console.warn('[session-view] error parsing data message', e)
      }
    }

    room.on(RoomEvent.DataReceived, onDataReceived)
    return () => {
      room.off(RoomEvent.DataReceived, onDataReceived)
    }
  }, [room])

  // Prepare auth payload publisher (re-send on agent join)
  const publishAuth = useCallback(async () => {
    try {
      const token = await getToken({ template: 'convex', skipCache: true })
      const email = user?.primaryEmailAddress?.emailAddress || user?.emailAddresses?.[0]?.emailAddress
      const name = user?.fullName || user?.firstName || user?.username || 'User'
      const payload = new TextEncoder().encode(JSON.stringify({ type: 'auth', token, email, name }))
      const isConnected = (room as unknown as { state?: string })?.state?.toLowerCase?.() === 'connected'
      if (!isConnected) return
      await room.localParticipant.publishData(payload, { reliable: true })
    } catch (e) {
      console.warn('[session-view] auth publish failed', (e as Error)?.message || e)
    }
  }, [getToken, user, room])

  // Send auth on initial connection and whenever a remote participant joins (agent may connect after us)
  useEffect(() => {
    const onConnected = () => { void publishAuth() }
    const onParticipantConnected = () => { void publishAuth() }
    room.on(RoomEvent.Connected, onConnected)
    room.on(RoomEvent.ParticipantConnected, onParticipantConnected)
    const isConnected = (room as unknown as { state?: string })?.state?.toLowerCase?.().includes('connected') === true
    if (isConnected) { void publishAuth() }
    return () => {
      room.off(RoomEvent.Connected, onConnected)
      room.off(RoomEvent.ParticipantConnected, onParticipantConnected)
    }
  }, [room, publishAuth])

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }, [conversation])

  return (
    <div className="absolute inset-0 flex flex-col">
      <div className="flex items-center justify-between px-3 py-2 border-b bg-white/80">
        <div className="font-medium">{appConfig.pageTitle}</div>
        <button
          onClick={onDisconnect}
          className="text-xs rounded px-2 py-1 bg-red-600 text-white hover:bg-red-700"
        >
          End
        </button>
      </div>
      
      {/* Conversation Log */}
      <div className="flex-1 flex flex-col">
        <div className="flex-1 overflow-y-auto p-3 space-y-3">
          {conversation.length === 0 ? (
            <div className="text-xs text-slate-500 italic">
              Conversation will appear here...
            </div>
          ) : (
            conversation.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div
                  className={`max-w-[85%] rounded-lg px-3 py-2 text-sm ${
                    message.role === 'user'
                      ? 'bg-emerald-100 text-emerald-900'
                      : 'bg-slate-100 text-slate-900'
                  }`}
                >
                  <div className="whitespace-pre-wrap">{message.content}</div>
                  <div className={`text-xs mt-1 ${
                    message.role === 'user' ? 'text-emerald-700' : 'text-slate-500'
                  }`}>
                    {message.role === 'user' ? 'You' : 'Assistant'} • {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
        
        <div className="px-3 py-2 border-t bg-slate-50 text-xs text-slate-500 flex items-center gap-3">
          <button
            onClick={() => setMicActive((v) => !v)}
            className={`text-xs rounded px-2 py-1 ${micActive ? 'bg-amber-600 hover:bg-amber-700' : 'bg-emerald-600 hover:bg-emerald-700'} text-white`}
          >
            {micActive ? 'Turn Mic Off' : 'Turn Mic On'}
          </button>
          <span>{micActive ? 'Mic is on. Speak to the agent.' : 'Mic is off. Turn on to start chatting.'}</span>
        </div>
      </div>
    </div>
  )
}
