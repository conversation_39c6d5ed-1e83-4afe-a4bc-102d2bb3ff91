import React from 'react'
import { App } from '@/components/app'
import type { AppConfig } from '@/lib/types'

const wigglewalkAppConfig: AppConfig = {
  pageTitle: 'WiggleWalk Assistant',
  pageDescription: 'Your friendly dog walking assistant.',
  companyName: 'WiggleWalk',
  supportsChatInput: false,
  supportsVideoInput: false,
  supportsScreenShare: false,
  isPreConnectBufferEnabled: true,
  logo: '/logo.svg',
  startButtonText: 'Talk to WiggleWalk',
}

export function VoiceAssistant() {
  const [open, setOpen] = React.useState(false)

  if (!open) {
    return (
      <button
        onClick={() => setOpen(true)}
        className="fixed bottom-4 right-4 z-[70] rounded-full px-4 py-2 bg-emerald-600 text-white shadow-lg"
        aria-label="Open Voice Assistant"
      >
        Voice
      </button>
    )
  }

  return (
    <div className="fixed bottom-4 right-4 z-[70] w-[380px] max-w-[90vw] h-[600px] max-h-[80vh] bg-white border rounded-xl shadow-xl overflow-hidden flex flex-col">
      <div className="flex items-center justify-between px-3 py-2 border-b">
        <div className="font-medium">WiggleWalk Assistant</div>
        <button onClick={() => setOpen(false)} className="text-slate-500 hover:text-slate-800">✕</button>
      </div>
      <div className="flex-grow relative">
        <App appConfig={wigglewalkAppConfig} />
      </div>
    </div>
  )
}


