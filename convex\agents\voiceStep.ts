import { action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";

export const step = action({
  args: {
    input: v.string(),
    user: v.optional(
      v.object({ subject: v.optional(v.string()), email: v.optional(v.string()), name: v.optional(v.string()) })
    ),
  },
  handler: async (ctx, { input }): Promise<{ say: string; status?: string }> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return { say: "Please sign in with Google on the site so I can help you with bookings." };
    }
    const text = input.trim();
    if (!text) return { say: "Could you repeat that?" };

    const result = (await ctx.runAction(api.agents.dogWalkerActions.talk, { input })) as {
      text?: string;
      threadId?: string;
    };

    return { say: String(result?.text || "") };
  },
});


