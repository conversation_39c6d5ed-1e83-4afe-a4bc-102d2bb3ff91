import { useState } from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RoomAudioRenderer } from '@livekit/components-react'
import type { AppConfig } from '@/lib/types'
import { useConnectionDetails } from '@/hooks/useConnectionDetails'
import { motion } from 'motion/react'
import { SessionView } from '@/components/session-view'

type AppProps = { appConfig: AppConfig }

export function App({ appConfig }: AppProps) {
  const { connectionDetails, loading, error, refreshConnectionDetails } = useConnectionDetails()
  const [sessionStarted, setSessionStarted] = useState(false)
  const [sessionKey, setSessionKey] = useState(0)

  if (loading) {
    return (
      <div className="flex h-full items-center justify-center text-sm text-slate-600">Connecting…</div>
    )
  }

  if (error) {
    return (
      <div className="flex h-full flex-col items-center justify-center gap-2 p-4 text-center">
        <div className="text-red-600 text-sm">{error}</div>
        <button onClick={() => refreshConnectionDetails()} className="px-3 py-1.5 rounded bg-slate-900 text-white text-sm">Retry</button>
      </div>
    )
  }

  if (!connectionDetails) return null

  return (
    <div className="h-full w-full relative">
      <motion.div
        className="absolute inset-0 z-10 flex items-center justify-center bg-white/90"
        initial={{ opacity: 1 }}
        animate={{ opacity: sessionStarted ? 0 : 1, pointerEvents: sessionStarted ? 'none' : 'auto' }}
        transition={{ duration: 0.25, ease: 'linear' }}
      >
        <div className="rounded-xl border bg-white shadow p-5 text-center max-w-sm w-[90%]">
          <div className="font-semibold text-lg">{appConfig.pageTitle}</div>
          {appConfig.pageDescription ? (
            <div className="mt-1 text-xs text-slate-500">{appConfig.pageDescription}</div>
          ) : null}
          <button
            onClick={async () => {
              try {
                await refreshConnectionDetails()
              } catch {}
              setSessionKey((k) => k + 1)
              setSessionStarted(true)
            }}
            className="mt-4 inline-flex items-center justify-center rounded-md bg-emerald-600 px-4 py-2 text-white text-sm hover:bg-emerald-700"
          >
            {appConfig.startButtonText || 'Start'}
          </button>
        </div>
      </motion.div>

      {sessionStarted && (
        <LiveKitRoom
          audio
          video={false}
          token={connectionDetails.participantToken}
          serverUrl={connectionDetails.serverUrl}
          connect
          key={sessionKey}
          className="h-full w-full"
        >
          <RoomAudioRenderer />
          <SessionView
            appConfig={appConfig}
            sessionStarted={sessionStarted}
            onDisconnect={() => {
              setSessionStarted(false)
              // prepare fresh token for next session
              void refreshConnectionDetails()
            }}
          />
        </LiveKitRoom>
      )}
    </div>
  )
}

// AutoPublishMic removed; SessionView handles mic publishing


