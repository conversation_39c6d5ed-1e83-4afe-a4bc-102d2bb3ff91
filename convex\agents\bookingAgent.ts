// convex/agents/bookingAgent.ts
// Placeholder booking helpers until @convex-dev/agent is wired
import type { ActionCtx } from "../_generated/server";
import { action } from "../_generated/server";
import { v } from "convex/values";
import { api } from "../_generated/api";
import { z } from "zod";
import { BookingIntentSchema, heuristicParse as clientHeuristic } from "../../src/lib/bookingIntent";
import OpenAI from "openai";

export type BookingPreview = {
  dates: string[];
  durationMins: number;
  dogs: string[];
  neighborhood?: string;
};

async function parseBookingIntent(_ctx: ActionCtx, input: string): Promise<z.infer<typeof BookingIntentSchema>> {
  // Fallback: no API key or disabled
  if (!process.env.OPENAI_API_KEY) {
    const heuristic = clientHeuristic(input);
    return BookingIntentSchema.parse(heuristic);
  }
  try {
    const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
    const modelId = process.env.AI_MODEL || "gpt-5-nano";
    const sys = `Extract a dog walking booking intent as strict JSON.\nFields: {"durationMins": 30|60|90, "dates": ["YYYY-MM-DD"...], "dogs": ["name"...], "neighborhood"?: string}.\nOnly output JSON, no prose.`;
    const completion = await openai.chat.completions.create({
      model: modelId,
      messages: [
        { role: "system", content: sys },
        { role: "user", content: input },
      ],
    });
    const content = completion.choices[0]?.message?.content || "{}";
    let json: unknown;
    try { json = JSON.parse(content); } catch { json = {}; }
    return BookingIntentSchema.parse(json);
  } catch {
    // Quota/429 or other error → heuristic fallback
    const heuristic = clientHeuristic(input);
    return BookingIntentSchema.parse(heuristic);
  }
}

// No local heuristic; reuse shared version

export async function getBookingPreview(ctx: ActionCtx, input: string): Promise<BookingPreview> {
  const parsed = await parseBookingIntent(ctx, input);
  let dogs = parsed.dogs;
  // If user did not specify dogs, try to default to their existing dog from DB
  try {
    if (!dogs?.length) {
      const identity = await ctx.auth.getUserIdentity();
      const email = identity?.email || "";
      if (email) {
        const owner = (await ctx.runQuery(api.owners.getByEmail, { email })) as unknown as { _id?: string } | null;
        if (owner?._id) {
          const records = (await ctx.runQuery((api.dogs.getByOwner as unknown) as never, { ownerId: owner._id } as never)) as ReadonlyArray<{ name?: string }>;
          const firstDogName = (records.find((d) => typeof d?.name === "string" && d.name.trim().length > 0)?.name || "").trim();
          if (firstDogName) {
            dogs = [firstDogName];
          }
        }
      }
    }
  } catch {
    // best-effort only
  }
  return {
    dates: parsed.dates,
    durationMins: parsed.durationMins,
    dogs: dogs || [],
    neighborhood: parsed.neighborhood,
  };
}

// Debug helper to verify LLM vs heuristic path
export const debugParseBooking = action({
  args: { input: v.string() },
  handler: async (ctx, { input }) => {
    const hasKey = !!process.env.OPENAI_API_KEY;
    if (!hasKey) {
      const parsed = BookingIntentSchema.parse(clientHeuristic(input));
      return { source: "heuristic" as const, parsed };
    }
    try {
      const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
      const modelId = process.env.AI_MODEL || "gpt-5-nano";
      const sys = `Extract a dog walking booking intent as strict JSON.\nFields: {"durationMins": 30|60|90, "dates": ["YYYY-MM-DD"...], "dogs": ["name"...], "neighborhood"?: string}.\nOnly output JSON, no prose.`;
      const completion = await openai.chat.completions.create({
        model: modelId,
        messages: [
          { role: "system", content: sys },
          { role: "user", content: input },
        ],
      });
      const raw = completion.choices[0]?.message?.content || "{}";
      let json: unknown; try { json = JSON.parse(raw); } catch { json = {}; }
      const parsed = BookingIntentSchema.parse(json);
      return { source: "llm" as const, parsed, raw };
    } catch (e) {
      const parsed = BookingIntentSchema.parse(clientHeuristic(input));
      return { source: "heuristic" as const, parsed, error: (e as Error)?.message };
    }
  },
});

export type AvailabilityResult = {
  date: string;
  slots: string[];
  price: number;
};

export async function searchAvailability(
  ctx: ActionCtx,
  params: { dates: string[]; durationMins: number }
): Promise<AvailabilityResult[]> {
  const results: AvailabilityResult[] = [];
  const price = getPricing({ durationMins: params.durationMins, dogsCount: 1 }).total;
  for (const date of params.dates) {
    try {
      const slots = (await ctx.runQuery(api.availability.getSlotsForDay, { date })) as string[];
      results.push({ date, slots, price });
    } catch {
      results.push({ date, slots: [], price });
    }
  }
  return results;
}

export function getPricing(params: { durationMins: number; dogsCount: number }): { total: number; breakdown: { base: number; perDog?: number } } {
  // Match UI pricing used in options: 30=$40, 60=$55, 90=$70; add $10 per extra dog
  const base = params.durationMins >= 90 ? 70 : params.durationMins >= 60 ? 55 : 40;
  const extraDogs = Math.max(0, params.dogsCount - 1);
  const perDog = extraDogs > 0 ? extraDogs * 10 : 0;
  return { total: base + perDog, breakdown: { base, perDog: perDog || undefined } };
}


