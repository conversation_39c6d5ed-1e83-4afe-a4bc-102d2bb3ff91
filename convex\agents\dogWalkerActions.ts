"use node";
// convex/agents/dogWalkerActions.ts
import { action } from "../_generated/server";
import { v } from "convex/values";
import { api, internal } from "../_generated/api";
import { dogWalker } from "./dogWalkerAgent";

export const talk = action({
  args: { input: v.string() },
  handler: async (ctx, { input }): Promise<{ text: string; threadId: string }> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity?.email || !identity.subject) {
      return { text: "Please sign in to chat with the assistant.", threadId: "" };
    }
    const userId = identity.subject;
    try {
      console.log("[talk] identity", {
        email: identity.email,
        subject: identity.subject,
        name: identity.name,
      });
    } catch {}

    // Ensure a persistent thread per user/agent
    const { threadId } = (await ctx.runMutation(internal.qa.getOrCreateThreadForUser, {
      userId,
      agentName: "dogWalker",
    })) as { threadId: string };

    // Ensure owner exists and has clerkId
    let owner = await ctx.runQuery(api.owners.getByEmail, { email: identity.email });
    if (!owner && identity.subject) {
      try {
        await ctx.runMutation(internal.booking.createOwnerFromClerk, {
          email: identity.email,
          name: identity.name || "User",
          clerkId: identity.subject,
        });
      } catch (e) {
        try { console.warn("[talk] createOwnerFromClerk failed", (e as Error)?.message || e); } catch {}
      }
      owner = await ctx.runQuery(api.owners.getByEmail, { email: identity.email });
    } else if (owner && !owner.clerkId && identity.subject) {
      try {
        await ctx.runMutation(internal.booking.updateOwnerClerkId, { ownerId: owner._id, clerkId: identity.subject });
      } catch (e) {
        try { console.warn("[talk] updateOwnerClerkId failed", (e as Error)?.message || e); } catch {}
      }
    }

    // Build user context
    let userContext = `User: ${identity.name || "User"} <${identity.email}>\n`;
    if (owner) {
      userContext += `OwnerId: ${owner._id}\n`;
      const dogs = (await ctx.runQuery(api.dogs.getByOwner, { ownerId: owner._id })) as Array<{
        name?: string;
        breed?: string;
        notes?: string;
      }>;
      if (Array.isArray(dogs) && dogs.length) {
        userContext += `Dogs:\n`;
        for (const d of dogs) {
          userContext += `- ${d.name || "Dog"}${d.breed ? ` (${d.breed})` : ""}${d.notes ? ` - ${d.notes}` : ""}\n`;
        }
      } else {
        userContext += `Dogs: none on file\n`;
      }
      const intake = await ctx.runQuery(api.intakes.getLatestForOwner, { ownerId: owner._id });
      if (intake) {
        const p = intake.prerequisites || {};
        userContext += `Intake: status=${intake.status}; prereq[vaccines=${String(p.vaccines)}; license=${String(
          p.license
        )}; friendly=${String(p.friendly)}]\n`;
      } else {
        userContext += `Intake: none submitted\n`;
      }
    } else {
      userContext += `Owner: not registered\n`;
    }

    const { text } = await dogWalker.generateText(ctx, { threadId, userId }, {
      prompt: `\n[USER CONTEXT]\n${userContext}\n[/USER CONTEXT]\n\n${input}`,
    });
    try {
      console.log("[talk] reply", { threadId, textLength: (text || "").length });
    } catch {}
    return { text, threadId };
  },
});


