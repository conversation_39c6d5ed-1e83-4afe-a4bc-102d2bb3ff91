import { internalAction, internalMutation } from "./_generated/server";
import { v } from "convex/values";
import { internal } from "./_generated/api";

const GCAL_BASE = "https://www.googleapis.com/calendar/v3/calendars";

export const createCalendarEvent = internalAction({
  args: { bookingId: v.id("bookings") },
  handler: async (ctx, { bookingId }) => {
    const calendarId = process.env.GOOGLE_CALENDAR_CALENDAR_ID;
    const token = process.env.GOOGLE_CALENDAR_API_TOKEN;
    if (!calendarId || !token) return { skipped: true };

    const details = (await ctx.runQuery(internal.booking.getBookingDetailsForEmail, { bookingId })) as unknown as {
      booking?: { startTime: string; endTime?: string; durationMins: number; notes?: string };
      owner?: { name?: string; email?: string };
      dog?: { name?: string };
    };
    if (!details?.booking || !details.owner || !details.dog) return { ok: false, reason: "missing_context" };

    const start = details.booking.startTime;
    const end =
      details.booking.endTime || new Date(new Date(start).getTime() + details.booking.durationMins * 60000).toISOString();

    const res = await fetch(`${GCAL_BASE}/${encodeURIComponent(calendarId)}/events`, {
      method: "POST",
      headers: { "content-type": "application/json", Authorization: `Bearer ${token}` },
      body: JSON.stringify({
        summary: `Dog Walk: ${details.dog.name}`,
        description: `Owner: ${details.owner.name || ""} (${details.owner.email || ""})\nNotes: ${details.booking.notes || ""}`,
        start: { dateTime: start },
        end: { dateTime: end },
      }),
    });
    if (!res.ok) return { ok: false, status: res.status, text: await res.text() };

    const json = (await res.json()) as { id?: string };
    if (json.id) await ctx.runMutation(internal.calendarSync.recordEventId, { bookingId, eventId: json.id });
    return { ok: true, eventId: json.id };
  },
});

export const deleteCalendarEvent = internalAction({
  args: { eventId: v.string() },
  handler: async (_ctx, { eventId }) => {
    const calendarId = process.env.GOOGLE_CALENDAR_CALENDAR_ID;
    const token = process.env.GOOGLE_CALENDAR_API_TOKEN;
    if (!calendarId || !token) return { skipped: true };
    const res = await fetch(
      `${GCAL_BASE}/${encodeURIComponent(calendarId)}/events/${encodeURIComponent(eventId)}`,
      {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      }
    );
    return { ok: res.ok, status: res.status };
  },
});

export const recordEventId = internalMutation({
  args: { bookingId: v.id("bookings"), eventId: v.string() },
  handler: async (ctx, { bookingId, eventId }) => {
    await ctx.db.patch(bookingId, { calendarEventId: eventId });
  },
});


