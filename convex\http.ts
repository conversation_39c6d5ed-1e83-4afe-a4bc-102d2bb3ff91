import { httpRouter } from "convex/server";
import type { FunctionReference } from "convex/server";
import type { ActionCtx } from "./_generated/server";
import { httpAction } from "./_generated/server";
import { api, internal } from "./_generated/api";

const VOICE_CONNECT_URL = process.env.VOICE_CONNECT_URL; // e.g. https://api.daily.co/v1/bots/start
const VOICE_API_KEY = process.env.VOICE_API_KEY; // e.g. Daily or Pipecat API key
const AI_MODEL = process.env.AI_MODEL || "gpt-4o-mini";
const VOICE_STT = process.env.VOICE_STT || "deepgram"; // set to "cartesia" to use Cartesia STT via Pipecat server
const VOICE_TTS = process.env.VOICE_TTS || "cartesia"; // set to "cartesia" to use Cartesia TTS via Pipecat server
const VOICE_LLM = process.env.VOICE_LLM || "openai";

function jsonResponse(body: unknown, init?: ResponseInit): Response {
  return new Response(JSON.stringify(body), {
    ...(init || {}),
    headers: { "content-type": "application/json", ...(init?.headers || {}) },
  });
}

const getSchemas = httpAction(async (ctx) => {
  const schemas = await ctx.runAction(api.deepgram.functionsSchema, {});
  return jsonResponse(schemas, { status: 200 });
});

export const connect = httpAction(async (ctx: ActionCtx, req: Request): Promise<Response> => {
  if (!VOICE_CONNECT_URL || !VOICE_API_KEY) {
    return jsonResponse({ error: "VOICE_CONNECT_URL or VOICE_API_KEY not set on server" }, { status: 500 });
  }
  const body = (await req.json().catch(() => ({}))) as { label?: string; userName?: string };
  const { functions } = (await ctx.runAction(api.deepgram.functionsSchema, {})) as { functions: unknown[] };
  const payload = {
    label: body?.label || "wigglewalk-agent",
    services: { stt: VOICE_STT, llm: VOICE_LLM, tts: VOICE_TTS, model: AI_MODEL },
    functions,
    user: { name: body?.userName || "guest" },
  };
  const res = await fetch(VOICE_CONNECT_URL, {
    method: "POST",
    headers: { Authorization: `Bearer ${VOICE_API_KEY}`, "content-type": "application/json" },
    body: JSON.stringify(payload),
  });
  if (!res.ok) return jsonResponse({ error: "connect_upstream", status: res.status, body: await res.text() }, { status: 502 });
  return jsonResponse(await res.json(), { status: 200 });
});

export const call = httpAction(async (ctx: ActionCtx, req: Request): Promise<Response> => {
  const body = (await req.json().catch(() => ({}))) as { event?: { type: "ping" | "function_call_request"; requestId?: string; name?: string; args?: unknown } };
  if (!body?.event) return jsonResponse({ error: "missing event" }, { status: 400 });
  const result = await ctx.runAction(api.deepgram.handleVoiceSession, { event: body.event as unknown as { type: "ping" | "function_call_request"; requestId?: string; name?: string; args?: unknown } });
  return jsonResponse({ result }, { status: 200 });
});

// Proxy a plain text command to the Convex commands handler
export const command = httpAction(async (ctx: ActionCtx, req: Request): Promise<Response> => {
  const body = (await req.json().catch(() => ({}))) as { input?: string };
  const input = (body?.input || "").trim();
  if (!input) return jsonResponse({ error: "missing_input" }, { status: 400 });
  const result = await ctx.runAction(api.commands.handleCommand, { input });
  return jsonResponse(result, { status: 200 });
});

// Talk to the dogWalker agent (same logic/tools as the voice agent) using text
export const talk = httpAction(async (ctx: ActionCtx, req: Request): Promise<Response> => {
  const body = (await req.json().catch(() => ({}))) as { input?: string };
  const input = (body?.input || "").trim();
  if (!input) return jsonResponse({ error: "missing_input" }, { status: 400 });
  try {
    const mod = (api as unknown as Record<string, unknown>)["agents/dogWalkerActions"] as
      | Record<string, unknown>
      | undefined;
    const ref = (mod?.talk as unknown) as FunctionReference<"action", "public">;
    const result = await ctx.runAction(ref, { input });
    return jsonResponse(result, { status: 200 });
  } catch {
    // Fallback to command handler and map to simple text
    try {
      const ui = (await ctx.runAction(api.commands.handleCommand, { input })) as unknown as
        | { ui?: string; data?: unknown }
        | string;
      const toText = (payload: { ui?: string; data?: unknown } | string): string => {
        try {
          if (typeof payload === "string") return payload;
          const kind = payload?.ui;
          const data = payload?.data as unknown;
          if (kind === "answer_card" && typeof (data as { answer?: unknown })?.answer !== "undefined")
            return String((data as { answer?: unknown })?.answer);
          if (kind === "options" && Array.isArray(data)) {
            const total = (data as Array<{ date?: string; time?: string }>).length;
            if (total === 0)
              return "I could not find any available times. Would you like to try another day or time?";
            const picks = (data as Array<{ date?: string; time?: string }>).slice(0, 3);
            const phrase = picks.map((o) => `${o.date} at ${o.time}`).join(", ");
            return `I found ${total} options. For example: ${phrase}. Which one works for you?`;
          }
          if (kind === "booking_preview" && data && typeof data === "object") {
            const d = data as { durationMins?: number; dogs?: string[]; dates?: string[] };
            const mins = d.durationMins ? `${d.durationMins}-minute` : "a";
            const dogs = Array.isArray(d.dogs) && d.dogs.length ? d.dogs.join(", ") : "your dog";
            const when = Array.isArray(d.dates) && d.dates.length ? ` on ${d.dates.join(", ")}` : "";
            return `You want a ${mins} walk for ${dogs}${when}. Should I check availability?`;
          }
          if (kind === "booking_confirm" && data && typeof data === "object") {
            const d = data as { total?: number };
            const total = typeof d.total === "number" ? `$${d.total}` : "the shown total";
            return `Great, the total is ${total}. Would you like me to finalize the booking now?`;
          }
          if (kind === "error" && data && typeof (data as { message?: unknown }).message !== "undefined")
            return `Sorry, ${String((data as { message?: unknown }).message)}. Could you rephrase or provide a bit more detail?`;
          if (data && typeof (data as { answer?: unknown }).answer !== "undefined")
            return String((data as { answer?: unknown }).answer);
          return "";
        } catch {
          return "";
        }
      };
      const text = toText(ui);
      return jsonResponse({ text }, { status: 200 });
    } catch {
      return jsonResponse({ text: "Sorry, I had trouble answering that. Please try again." }, { status: 200 });
    }
  }
});

// Voice step: stateful agent step that can enforce intake and booking
export const voiceStep = httpAction(async (ctx: ActionCtx, req: Request): Promise<Response> => {
  const body = (await req.json().catch(() => ({}))) as { input?: string; user?: { subject?: string; email?: string; name?: string } };
  const input = (body?.input || "").trim();
  if (!input) return jsonResponse({ error: "missing_input" }, { status: 400 });
  const mod = (api as unknown as Record<string, unknown>)["agents/voiceStep"] as Record<string, unknown> | undefined;
  const ref = (mod?.step as unknown) as FunctionReference<"action", "public">;
  const result = await ctx.runAction(ref, { input, user: body?.user } as never);
  return jsonResponse(result, { status: 200 });
});

// LiveKit: Issue a participant token for the browser client
export const livekitToken = httpAction(async (ctx: ActionCtx): Promise<Response> => {
  const identity = await ctx.auth.getUserIdentity();
  if (!identity) {
    return jsonResponse({ error: "unauthenticated" }, { status: 401 });
  }

  const subject = identity.subject!;
  const name = identity.name ?? identity.email ?? "User";

  // rate limit: relaxed in dev, stricter in prod
  const key = `lk:${subject}`;
  const isProd = process.env.NODE_ENV === "production";
  const windowMs = isProd ? 5 * 60 * 1000 : 60 * 1000; // 5 min in prod, 1 min in dev
  const max = isProd ? 10 : 30; // 10 tokens/5m prod, 30 tokens/min dev
  const rl = await ctx.runMutation(internal.debug.checkRate, { key, windowMs, max });
  if (!rl.allowed) return jsonResponse({ error: "rate_limited" }, { status: 429 });

  const livekitMod = (api as unknown as Record<string, unknown>)["livekit"] as Record<string, unknown> | undefined;
  const issueTokenRef = (livekitMod?.issueToken as unknown) as FunctionReference<"action", "public">;
  const result = await ctx.runAction(issueTokenRef, { subject, name }) as { url: string; token: string; roomName: string };

  // Optional dispatch is disabled in http runtime to avoid Node-only imports

  return jsonResponse(result, { status: 200 });
});

const http = httpRouter();
http.route({ path: "/voice/connect", method: "POST", handler: connect });
http.route({ path: "/voice/schemas", method: "GET", handler: getSchemas });
http.route({ path: "/voice/call", method: "POST", handler: call });
http.route({ path: "/command", method: "POST", handler: command });
http.route({ path: "/voice/talk", method: "POST", handler: talk });
http.route({ path: "/voice/step", method: "POST", handler: voiceStep });
http.route({ path: "/livekit-token", method: "GET", handler: livekitToken });
export default http;
