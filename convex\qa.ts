import type { ActionCtx } from "./_generated/server";
import { internalMutation } from "./_generated/server";
import { v } from "convex/values";
import OpenAI from "openai";

export type QAResult = { answer: string; sources: { title: string; url: string; snippet: string }[] };

export async function answerWithStrapi(_ctx: ActionCtx, query: string, baseUrl?: string): Promise<QAResult> {
  const STRAPI_URL = baseUrl || (process.env.STRAPI_URL as string) || "http://localhost:1337";
  // Fetch pages
  const res = await fetch(`${STRAPI_URL}/api/pages?populate=*`).catch(() => null);
  let pages: Array<{ title: string; slug: string; body: string } | null> = [];
  if (res && res.ok) {
    const json = (await res.json()) as { data?: any[] };
    pages = (json.data || []).map((item) => {
      const a = item?.attributes || item || {};
      const title = a.title || a.metaTitle || a.slug || "Page";
      const slug = a.slug || String(item?.id || "");
      const body = JSON.stringify(a);
      return { title, slug, body };
    });
  }

  // Naive ranking by query substring count
  const scored = pages
    .filter(Boolean)
    .map((p) => ({
      page: p!,
      score: (p!.body.toLowerCase().match(new RegExp(query.toLowerCase().split(/\s+/).join("|"), "g")) || []).length,
    }))
    .sort((a, b) => b.score - a.score)
    .slice(0, 4);

  const context = scored
    .map(({ page }, i) => `Source ${i + 1} (${page.title}):\n${page.body.slice(0, 1200)}`)
    .join("\n\n");

  const openai = new OpenAI({ apiKey: process.env.OPENAI_API_KEY });
  const modelId = process.env.AI_MODEL || "gpt-5-nano";
  const prompt = `Use the sources to answer the user's question succinctly. If uncertain, say so.\n\nSources:\n${context}\n\nQuestion: ${query}`;
  const completion = await openai.chat.completions.create({
    model: modelId,
    messages: [
      { role: "system", content: "You are WiggleWalk's helpful assistant. Be concise and accurate." },
      { role: "user", content: prompt },
    ],
  });
  const answer = completion.choices[0]?.message?.content || "";

  const sources = scored.map(({ page }) => ({
    title: page.title,
    url: `${STRAPI_URL}/pages/${page.slug}`,
    snippet: page.body.slice(0, 200),
  }));

  return { answer, sources };
}

// Thread mapping helpers for agents (write-capable)
export const getOrCreateThreadForUser = internalMutation({
  args: { userId: v.string(), agentName: v.string() },
  handler: async (ctx, { userId, agentName }) => {
    const existing = await ctx.db
      .query("agentThreads")
      .withIndex("by_user_agent", (q) => q.eq("userId", userId).eq("agentName", agentName))
      .first();
    if (existing) {
      return { threadId: existing.threadId, isNew: false as const };
    }

    const threadId = crypto.randomUUID();
    await ctx.db.insert("agentThreads", {
      userId,
      agentName,
      threadId,
      updatedAt: Date.now(),
    });

    return { threadId, isNew: true as const };
  },
});


