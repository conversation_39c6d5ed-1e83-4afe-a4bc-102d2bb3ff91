# WiggleWalk Environment Variables

This document organizes all environment variables used in the WiggleWalk project, grouped by service and purpose for better management and understanding.

## Table of Contents
- [Core Services](#core-services)
- [Voice/AI Services](#voiceai-services)
- [Frontend Configuration](#frontend-configuration)
- [Development & Deployment](#development--deployment)
- [Security & Authentication](#security--authentication)
- [Voice Agent Configuration](#voice-agent-configuration)

## Core Services

### Convex
```bash
# Backend service URL
CONVEX_SITE_URL=https://glorious-lapwing-113.convex.site

# Deployment identifier (for npx convex dev)
CONVEX_DEPLOYMENT=dev:glorious-lapwing-113
```

### LiveKit (Voice/Video)
```bash
# LiveKit server configuration
LIVEKIT_URL=wss://wigglewalk-w468g24l.livekit.cloud
LIVEKIT_API_KEY=APIfRsGHvZp9SeP
LIVEKIT_API_SECRET=TjRFUuKQVfJvm52ine2H8ChfD8IiCE7HKSCiiCIucejC

# Bot token for automated agents
LIVEKIT_BOT_TOKEN=eyJhbGciOiJIUzI1NiJ9...
```

## Voice/AI Services

### OpenAI
```bash
OPENAI_API_KEY=********************************************************************************************************************************************************************
```

### Deepgram (Speech-to-Text)
```bash
DEEPGRAM_API_KEY=****************************************
```

### Cartesia (Text-to-Speech)
```bash
CARTESIA_API_KEY=sk_car_s9TDLY42TMpn4e4xAQMVLY
```

## Frontend Configuration

### Vite Environment Variables
```bash
# Convex frontend configuration
VITE_CONVEX_URL=https://glorious-lapwing-113.convex.cloud
VITE_CONVEX_HTTP_URL=https://glorious-lapwing-113.convex.site

# Clerk authentication (safe for frontend)
VITE_CLERK_PUBLISHABLE_KEY=pk_test_c2F2aW5nLXB1Zy01My5jbGVyay5hY2NvdW50cy5kZXYk

# Strapi CMS integration
VITE_STRAPI_URL=http://localhost:1337

# Cartesia TTS configuration
VITE_CARTESIA_API_KEY=sk_car_s9TDLY42TMpn4e4xAQMVLY
VITE_CARTESIA_MODEL=sonic-2
VITE_CARTESIA_VOICE_ID=794f9389-aac1-45b6-b726-9d9369183238

# Voice assistant configuration
VITE_VOICE_WS_URL=ws://localhost:8787
VITE_VAD_THRESHOLD=0.02
VITE_VAD_SILENCE_MS=1200
VITE_VAD_CHECK_MS=200

# Debug settings
VITE_DEBUG=off
VITE_DEBUG_AUDIO=off

# HTTPS configuration (SSL certificates)
VITE_HTTPS=on
VITE_SSL_KEY=LS0tLS1CRUdJTiBSU0EgUFJJVkFURSBLRVktLS0tLQ... (truncated)
VITE_SSL_CERT=LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t... (truncated)
```

## Development & Deployment

### WebSocket Bridge
```bash
WS_BRIDGE_PORT=8787
```

## Security & Authentication

### API Keys Summary
- **OpenAI**: Used for LLM and embeddings
- **Deepgram**: Used for speech-to-text processing
- **Cartesia**: Used for text-to-speech synthesis
- **LiveKit**: Used for real-time voice/video communication

### Authentication Services
- **Clerk**: User authentication and management
- **LiveKit JWT**: Room access tokens for secure connections

## Voice Agent Configuration

### Session Management
```bash
# Maximum session duration in seconds (default: 30 minutes)
MAX_SESSION_DURATION=1800

# Idle timeout in seconds (default: 5 minutes)  
IDLE_TIMEOUT=300
```

## File Distribution

### .env (Shared/Server-side)
Contains server-side configuration and secrets that should not be exposed to the frontend:
- `CONVEX_SITE_URL`
- `DEEPGRAM_API_KEY`
- `CARTESIA_API_KEY`
- `OPENAI_API_KEY`
- `LIVEKIT_URL`
- `LIVEKIT_API_KEY`
- `LIVEKIT_API_SECRET`
- `LIVEKIT_BOT_TOKEN`
- `WS_BRIDGE_PORT`
- `MAX_SESSION_DURATION`
- `IDLE_TIMEOUT`

### .env.local (Local Development)
Contains local development overrides and frontend configuration:
- All `VITE_*` variables (safe for frontend)
- Local development overrides
- Development-specific configurations

## Best Practices

1. **Security**: Never commit actual API keys to version control
2. **Environment Separation**: Use different files for different environments (.env.development, .env.production)
3. **Documentation**: Keep this file updated when adding new environment variables
4. **Grouping**: Variables are grouped by service for easier management
5. **Comments**: Add comments to explain the purpose of complex or non-obvious variables

## Common Issues

1. **Duplicate Keys**: Check both `.env` and `.env.local` for duplicate variables
2. **Frontend vs Backend**: Only `VITE_*` variables are exposed to the frontend
3. **SSL Certificates**: Large certificate values may need special handling in some environments
