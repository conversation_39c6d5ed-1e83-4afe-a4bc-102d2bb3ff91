// convex/schema.ts
import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // User authentication table (separate from owners for flexibility)
  users: defineTable({
    clerkId: v.optional(v.string()), // Clerk user ID
    email: v.string(),
    name: v.optional(v.string()),
    role: v.optional(v.union(
      v.literal("customer"),
      v.literal("walker"), 
      v.literal("admin")
    )),
    createdAt: v.optional(v.number()),
    lastLoginAt: v.optional(v.number()),
    // Legacy fields from existing data
    image: v.optional(v.string()),
    emailVerificationTime: v.optional(v.number()),
  })
    .index("by_clerk_id", ["clerkId"])
    .index("by_email", ["email"]),

  // Existing tables
  owners: defineTable({
    name: v.string(),
    email: v.string(),
    phone: v.optional(v.string()),
    clerkId: v.optional(v.string()),
  })
    .index("by_email", ["email"])
    .index("by_clerk_id", ["clerkId"]),
  
  dogs: defineTable({
    name: v.string(),
    breed: v.optional(v.string()),
    notes: v.optional(v.string()),
    ownerId: v.id("owners"),
    age: v.optional(v.number()),
    weight: v.optional(v.number()),
    temperament: v.optional(v.array(v.string())),
    recall: v.optional(v.union(v.literal("reliable"), v.literal("ok"), v.literal("needs_work"))),
    carRide: v.optional(v.union(v.literal("calm"), v.literal("ok"), v.literal("anxious"))),
    leash: v.optional(v.union(v.literal("pulls"), v.literal("ok"), v.literal("loose"))),
    triggers: v.optional(v.array(v.string())),
    healthNeeds: v.optional(v.string()),
    schedulePref: v.optional(v.object({ days: v.array(v.string()), windows: v.array(v.string()) })),
  })
    .index("by_owner", ["ownerId"]),
  
  // Enhanced bookings table for calendar
  bookings: defineTable({
    dogId: v.id("dogs"),
    ownerId: v.id("owners"),
    walkerId: v.optional(v.id("walkers")), // Assigned walker
    startTime: v.string(), // ISO format
    endTime: v.optional(v.string()), // ISO format for calendar events
    durationMins: v.number(),
    status: v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("in-progress"),
      v.literal("completed"),
      v.literal("cancelled"),
    ),
    paymentUrl: v.optional(v.string()),
    stripeId: v.optional(v.string()),
    price: v.optional(v.number()),
    reminderSent: v.optional(v.boolean()),
    createdAt: v.optional(v.string()),
    cancellationReason: v.optional(v.string()),
    cancelledAt: v.optional(v.string()),
    // Calendar-specific fields
    notes: v.optional(v.string()), // Walk notes
    location: v.optional(v.string()), // Walk location
    recurringBookingId: v.optional(v.id("recurringBookings")), // For recurring walks
    calendarEventId: v.optional(v.string()),
  })
    .index("by_status_and_time", ["status", "startTime"])
    .index("by_start_time", ["startTime"])
    .index("by_end_time", ["endTime"])
    .index("by_owner", ["ownerId"])
    .index("by_walker", ["walkerId"])
    .index("by_date_range", ["startTime", "endTime"]),

  // New tables for calendar functionality
  walkers: defineTable({
    name: v.string(),
    email: v.string(),
    phone: v.optional(v.string()),
    isActive: v.boolean(),
    hourlyRate: v.optional(v.number()),
    specialties: v.optional(v.array(v.string())), // e.g., ["large dogs", "puppies"]
    maxDogsPerWalk: v.optional(v.number()),
  })
    .index("by_email", ["email"])
    .index("by_active", ["isActive"]),

  // Enhanced availability management
  availability: defineTable({
    walkerId: v.optional(v.id("walkers")), // Optional for global availability
    date: v.string(), // YYYY-MM-DD format
    timeSlots: v.array(v.object({
      hour: v.number(), // 24-hour format (9, 10, 11, etc.)
      timeRange: v.string(), // "09:00 - 10:00"
      isAvailable: v.boolean(),
      maxCapacity: v.number(), // How many dogs can be walked in this slot
      currentBookings: v.optional(v.number()), // Current number of bookings (calculated)
      isFullyBooked: v.optional(v.boolean()), // Calculated field
    })),
    isFullyBooked: v.boolean(),
    createdAt: v.optional(v.string()),
    updatedAt: v.optional(v.string()),
  })
    .index("by_walker_and_date", ["walkerId", "date"])
    .index("by_date", ["date"]),

  // Recurring booking patterns
  recurringBookings: defineTable({
    ownerId: v.id("owners"),
    dogId: v.id("dogs"),
    frequency: v.union(
      v.literal("weekly"),
      v.literal("daily"),
      v.literal("bi-weekly"),
    ),
    daysOfWeek: v.array(v.number()), // 0-6 for Sun-Sat
    startTime: v.string(), // HH:mm format
    durationMins: v.number(),
    startDate: v.string(), // YYYY-MM-DD when recurring starts
    endDate: v.optional(v.string()), // YYYY-MM-DD when recurring ends
    isActive: v.boolean(),
    createdAt: v.string(),
  })
    .index("by_owner", ["ownerId"])
    .index("by_active", ["isActive"]),

  // Intake pipeline
  intakes: defineTable({
    ownerId: v.id("owners"),
    dogId: v.id("dogs"),
    prerequisites: v.object({ vaccines: v.boolean(), license: v.boolean(), friendly: v.boolean() }),
    questionnaire: v.optional(v.object({
      temperament: v.optional(v.array(v.string())),
      recall: v.optional(v.string()),
      carRide: v.optional(v.string()),
      leash: v.optional(v.string()),
      triggers: v.optional(v.array(v.string())),
      healthNeeds: v.optional(v.string()),
      schedulePref: v.optional(v.object({ days: v.array(v.string()), windows: v.array(v.string()) })),
    })),
    introCall: v.optional(v.object({ preferredSlots: v.array(v.string()), scheduledAt: v.optional(v.string()) })),
    status: v.union(
      v.literal("intake_submitted"),
      v.literal("intro_call_scheduled"),
      v.literal("trial_booked"),
      v.literal("accepted"),
      v.literal("alternative_recommendation"),
      v.literal("declined")
    ),
    notes: v.optional(v.string()),
    history: v.optional(v.array(v.object({ at: v.number(), action: v.string(), by: v.optional(v.string()) }))),
    createdAt: v.number(),
  })
    .index("by_status", ["status"]) 
    .index("by_owner", ["ownerId"]) ,

  trials: defineTable({
    intakeId: v.id("intakes"),
    scheduledAt: v.string(),
    notes: v.optional(v.string()),
    result: v.optional(v.union(v.literal("passed"), v.literal("needs_private"), v.literal("declined"))),
    createdAt: v.number(),
  })
    .index("by_intake", ["intakeId"]),

  // Calendar settings and business rules
  businessSettings: defineTable({
    key: v.string(), // e.g., "operating_hours", "booking_rules"
    value: v.union(
      v.string(),
      v.number(),
      v.boolean(),
      v.object({
        startTime: v.string(),
        endTime: v.string(),
        daysOfWeek: v.array(v.number()),
      }),
    ),
    updatedAt: v.string(),
  })
    .index("by_key", ["key"]),

  // ============== CONTENT MANAGEMENT SYSTEM ==============
  
  // Pages - Main content pages like Home, Services, About
  pages: defineTable({
    title: v.string(),
    slug: v.string(), // URL-friendly identifier like "home", "services", "about"
    content: v.object({
      hero: v.optional(v.object({
        title: v.string(),
        subtitle: v.string(),
        description: v.optional(v.string()),
        ctaText: v.optional(v.string()),
        ctaLink: v.optional(v.string()),
        backgroundImage: v.optional(v.string()), // URL to image
      })),
      sections: v.array(v.object({
        id: v.string(),
        type: v.union(
          v.literal("text"),
          v.literal("feature_list"), 
          v.literal("testimonial"),
          v.literal("team_member"),
          v.literal("service_comparison"),
          v.literal("pricing_table"),
          v.literal("gallery"),
          v.literal("faq")
        ),
        title: v.optional(v.string()),
        content: v.union(
          v.string(), // For text sections
          v.object({
            text: v.optional(v.string()),
            items: v.optional(v.array(v.string())),
            features: v.optional(v.array(v.object({
              title: v.string(),
              description: v.string(),
              icon: v.optional(v.string()),
            }))),
            testimonial: v.optional(v.object({
              quote: v.string(),
              author: v.string(),
              role: v.optional(v.string()),
              image: v.optional(v.string()),
            })),
            teamMember: v.optional(v.object({
              name: v.string(),
              role: v.string(),
              bio: v.string(),
              image: v.optional(v.string()),
              certifications: v.optional(v.array(v.string())),
            })),
            comparison: v.optional(v.object({
              ourService: v.object({
                title: v.string(),
                price: v.string(),
                duration: v.string(),
                benefits: v.array(v.string()),
              }),
              competitor: v.object({
                title: v.string(),
                price: v.string(),
                duration: v.string(),
                benefits: v.array(v.string()),
              }),
            })),
            images: v.optional(v.array(v.object({
              url: v.string(),
              alt: v.string(),
              caption: v.optional(v.string()),
            }))),
            questions: v.optional(v.array(v.object({
              question: v.string(),
              answer: v.string(),
            }))),
          }),
        ),
        order: v.number(),
      })),
    }),
    metaTitle: v.optional(v.string()),
    metaDescription: v.optional(v.string()),
    isPublished: v.boolean(),
    createdAt: v.string(),
    updatedAt: v.string(),
    createdBy: v.optional(v.id("users")),
  })
    .index("by_slug", ["slug"])
    .index("by_published", ["isPublished"])
    .index("by_created_at", ["createdAt"]),

  // Services - Detailed service offerings
  services: defineTable({
    name: v.string(),
    slug: v.string(),
    shortDescription: v.string(),
    longDescription: v.string(),
    price: v.object({
      amount: v.number(),
      currency: v.string(), // "USD", "CAD"
      unit: v.string(), // "per walk", "per hour"
    }),
    duration: v.object({
      value: v.number(),
      unit: v.string(), // "minutes", "hours"
    }),
    features: v.array(v.object({
      title: v.string(),
      description: v.string(),
      included: v.boolean(),
    })),
    requirements: v.array(v.string()),
    serviceArea: v.array(v.string()), // ["New Westminster", "Burnaby"]
    maxDogs: v.number(),
    images: v.array(v.object({
      url: v.string(),
      alt: v.string(),
      isPrimary: v.boolean(),
    })),
    availability: v.object({
      daysOfWeek: v.array(v.number()), // 0-6 for Sun-Sat
      timeSlots: v.array(v.string()), // ["09:00-10:30", "11:00-12:30"]
    }),
    isActive: v.boolean(),
    order: v.number(), // Display order
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_slug", ["slug"])
    .index("by_active", ["isActive"])
    .index("by_order", ["order"]),

  // Gallery - Photo galleries for different locations/activities
  galleries: defineTable({
    title: v.string(),
    slug: v.string(),
    description: v.optional(v.string()),
    location: v.optional(v.string()),
    category: v.union(
      v.literal("pack_walk"),
      v.literal("private_walk"),
      v.literal("location"),
      v.literal("dogs"),
      v.literal("behind_scenes")
    ),
    images: v.array(v.object({
      url: v.string(),
      alt: v.string(),
      caption: v.optional(v.string()),
      takenAt: v.optional(v.string()),
      tags: v.optional(v.array(v.string())),
    })),
    coverImage: v.optional(v.string()), // URL to cover image
    isPublished: v.boolean(),
    order: v.number(),
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_slug", ["slug"])
    .index("by_category", ["category"])
    .index("by_published", ["isPublished"])
    .index("by_order", ["order"]),

  // Team Members - Steven, Gloria, future team members
  teamMembers: defineTable({
    name: v.string(),
    role: v.string(),
    bio: v.string(),
    shortBio: v.optional(v.string()), // For shorter displays
    image: v.optional(v.string()),
    certifications: v.array(v.object({
      name: v.string(),
      issuedBy: v.string(),
      issuedDate: v.optional(v.string()),
      expirationDate: v.optional(v.string()),
      credentialUrl: v.optional(v.string()),
    })),
    experience: v.object({
      yearsExperience: v.number(),
      specialties: v.array(v.string()),
      achievements: v.optional(v.array(v.string())),
    }),
    contact: v.optional(v.object({
      email: v.optional(v.string()),
      phone: v.optional(v.string()),
      linkedIn: v.optional(v.string()),
    })),
    isActive: v.boolean(),
    order: v.number(),
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_active", ["isActive"])
    .index("by_order", ["order"]),

  // Testimonials - Customer reviews and feedback
  testimonials: defineTable({
    clientName: v.string(),
    dogName: v.optional(v.string()),
    rating: v.number(), // 1-5 stars
    title: v.optional(v.string()),
    content: v.string(),
    serviceType: v.optional(v.string()), // Which service they used
    clientImage: v.optional(v.string()),
    dogImage: v.optional(v.string()),
    isPublished: v.boolean(),
    isFeatured: v.boolean(), // Show on homepage
    dateOfService: v.optional(v.string()),
    location: v.optional(v.string()), // Where the walk took place
    order: v.number(),
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_published", ["isPublished"])
    .index("by_featured", ["isFeatured"])
    .index("by_rating", ["rating"])
    .index("by_order", ["order"]),

  // FAQ - Frequently asked questions
  faqs: defineTable({
    question: v.string(),
    answer: v.string(),
    category: v.union(
      v.literal("general"),
      v.literal("booking"),
      v.literal("services"),
      v.literal("pricing"),
      v.literal("safety"),
      v.literal("requirements")
    ),
    isPublished: v.boolean(),
    order: v.number(),
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_category", ["category"])
    .index("by_published", ["isPublished"])
    .index("by_order", ["order"]),

  // Locations - Walking locations/parks
  locations: defineTable({
    name: v.string(),
    slug: v.string(),
    address: v.string(),
    description: v.string(),
    features: v.array(v.string()), // ["Off-leash area", "Water access", "Trails"]
    difficulty: v.union(
      v.literal("easy"),
      v.literal("moderate"),
      v.literal("challenging")
    ),
    suitableFor: v.array(v.string()), // ["All dogs", "Large dogs", "High energy dogs"]
    coordinates: v.optional(v.object({
      lat: v.number(),
      lng: v.number(),
    })),
    images: v.array(v.object({
      url: v.string(),
      alt: v.string(),
      caption: v.optional(v.string()),
    })),
    amenities: v.array(v.string()), // ["Parking", "Washrooms", "Dog waste bags"]
    bestTimes: v.array(v.string()), // ["Morning", "Afternoon"]
    isActive: v.boolean(),
    order: v.number(),
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_slug", ["slug"])
    .index("by_active", ["isActive"])
    .index("by_difficulty", ["difficulty"])
    .index("by_order", ["order"]),

  // Blog Posts - For SEO and customer education
  blogPosts: defineTable({
    title: v.string(),
    slug: v.string(),
    excerpt: v.string(),
    content: v.string(), // Markdown or rich text
    featuredImage: v.optional(v.string()),
    category: v.union(
      v.literal("dog_care"),
      v.literal("training_tips"),
      v.literal("local_guides"),
      v.literal("company_news"),
      v.literal("safety")
    ),
    tags: v.array(v.string()),
    metaTitle: v.optional(v.string()),
    metaDescription: v.optional(v.string()),
    isPublished: v.boolean(),
    publishedAt: v.optional(v.string()),
    readingTime: v.optional(v.number()), // Estimated reading time in minutes
    authorId: v.id("teamMembers"),
    createdAt: v.string(),
    updatedAt: v.string(),
  })
    .index("by_slug", ["slug"])
    .index("by_published", ["isPublished"])
    .index("by_category", ["category"])
    .index("by_author", ["authorId"])
    .index("by_published_date", ["publishedAt"]),

  // Site Settings - Global site configuration
  siteSettings: defineTable({
    key: v.string(), // "company_info", "contact_info", "social_media", "seo"
    value: v.union(
      v.string(),
      v.object({
        companyName: v.optional(v.string()),
        tagline: v.optional(v.string()),
        description: v.optional(v.string()),
        logo: v.optional(v.string()),
        favicon: v.optional(v.string()),
        primaryColor: v.optional(v.string()),
        secondaryColor: v.optional(v.string()),
        phone: v.optional(v.string()),
        email: v.optional(v.string()),
        address: v.optional(v.string()),
        serviceAreas: v.optional(v.array(v.string())),
        socialMedia: v.optional(v.object({
          facebook: v.optional(v.string()),
          instagram: v.optional(v.string()),
          twitter: v.optional(v.string()),
          linkedin: v.optional(v.string()),
        })),
        seo: v.optional(v.object({
          defaultTitle: v.optional(v.string()),
          defaultDescription: v.optional(v.string()),
          defaultImage: v.optional(v.string()),
          googleAnalyticsId: v.optional(v.string()),
        })),
      }),
    ),
    updatedAt: v.string(),
    updatedBy: v.optional(v.id("users")),
  })
    .index("by_key", ["key"]),

  // Simple rate limit buckets
  rateLimits: defineTable({
    key: v.string(), // e.g., "cmd:<subject>" or "lk:<subject>"
    windowStart: v.number(), // epoch ms of window start
    windowMs: v.number(),
    count: v.number(),
    updatedAt: v.number(),
  })
    .index("by_key", ["key"]),

  // Agent user thread mapping for persistent conversations
  agentThreads: defineTable({
    userId: v.string(),
    agentName: v.string(),
    threadId: v.string(),
    updatedAt: v.number(),
  })
    .index("by_user_agent", ["userId", "agentName"]),
});
