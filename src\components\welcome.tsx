import React from 'react'

type WelcomeProps = {
  startButtonText?: string
  onStartCall?: () => void
  className?: string
  style?: React.CSSProperties
}

export function Welcome({ startButtonText, onStartCall, className, style }: WelcomeProps) {
  return (
    <div className={`flex h-full w-full items-center justify-center ${className ?? ''}`} style={style}>
      <div className="rounded-xl border bg-white shadow p-5 text-center max-w-sm w-[90%]">
        <div className="font-semibold text-lg">WiggleWalk Assistant</div>
        <div className="mt-1 text-xs text-slate-500">Your friendly dog walking assistant.</div>
        <button
          onClick={onStartCall}
          className="mt-4 inline-flex items-center justify-center rounded-md bg-emerald-600 px-4 py-2 text-white text-sm hover:bg-emerald-700"
        >
          {startButtonText || 'Start'}
        </button>
      </div>
    </div>
  )
}


