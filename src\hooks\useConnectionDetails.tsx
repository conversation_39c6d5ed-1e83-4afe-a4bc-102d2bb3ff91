import { useCallback, useEffect, useState } from 'react'
import { useAuth } from '@clerk/clerk-react'

export type ConnectionDetails = {
  serverUrl: string
  participantToken: string
  roomName?: string
  participantName?: string
}

export function useConnectionDetails() {
  const [connectionDetails, setConnectionDetails] = useState<ConnectionDetails | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [loading, setLoading] = useState<boolean>(false)
  const { getToken, isSignedIn } = useAuth()

  const fetchConnectionDetails = useCallback(async () => {
    setLoading(true)
    setError(null)
    setConnectionDetails(null)
    try {
      // Require auth; Convex /livekit-token enforces Google sign-in
      if (!isSignedIn) {
        throw new Error('unauthenticated')
      }
      const clerkToken = await getToken({ template: 'convex', skipCache: true })
      if (!clerkToken) throw new Error('unauthenticated')
      const url = new URL('/api/livekit-token', window.location.origin)
      const res = await fetch(url.toString(), {
        method: 'GET',
        headers: { Authorization: `Bearer ${clerkToken}` },
      })
      if (!res.ok) throw new Error(`token ${res.status}`)
      const data = (await res.json()) as { url?: string; token?: string; roomName?: string; name?: string }
      const serverUrl = String(data?.url || '')
      const participantToken = String(data?.token || '')
      if (!serverUrl || !participantToken) throw new Error('missing token')
      setConnectionDetails({
        serverUrl,
        participantToken,
        roomName: data.roomName,
        participantName: data.name || 'user',
      })
    } catch (e) {
      setError((e as Error)?.message || 'Failed to fetch connection details')
    } finally {
      setLoading(false)
    }
  }, [getToken, isSignedIn])

  useEffect(() => {
    void fetchConnectionDetails()
  }, [fetchConnectionDetails])

  return { connectionDetails, loading, error, refreshConnectionDetails: fetchConnectionDetails }
}


