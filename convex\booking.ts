// convex/booking.ts
import { v } from "convex/values";
import { internalMutation, internalQuery, action, query, mutation } from "./_generated/server";
import { api, internal } from "./_generated/api";
import type { Id } from "./_generated/dataModel";

// --- Queries ---
// For the admin dashboard to view all bookings
export const list = query({
  handler: async (ctx) => {
    return await ctx.db.query("bookings").order("desc").collect();
  },
});


// --- Actions ---
// Creates multiple bookings for an authenticated user
export const bookMultipleWalksAuth = action({
  args: {
    bookings: v.array(v.object({
      startTime: v.string(),
      durationMins: v.number(),
    })),
    dogName: v.string(),
    dogBreed: v.optional(v.string()),
    dogNotes: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<string[]> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Must be signed in to book walks");
    }

    // Check if all time slots are still available before creating anything
    for (const booking of args.bookings) {
      const conflictCheck = await ctx.runQuery(api.booking.checkSlotAvailability, {
        startTime: booking.startTime,
        durationMins: booking.durationMins,
      });
      
      if (!conflictCheck.available) {
        throw new Error(`Time slot ${booking.startTime} is no longer available. ${conflictCheck.reason}`);
      }
    }

    // Get or create owner record
    const existingOwner = await ctx.runQuery(api.owners.getByEmail, {
      email: identity.email!,
    });
    
    let ownerId: Id<"owners">;
    if (existingOwner) {
      ownerId = existingOwner._id;
      
      if (!existingOwner.clerkId && identity.subject) {
        await ctx.runMutation(internal.booking.updateOwnerClerkId, {
          ownerId,
          clerkId: identity.subject,
        });
      }
    } else {
      ownerId = await ctx.runMutation(internal.booking.createOwnerFromClerk, {
        email: identity.email!,
        name: identity.name || "User",
        clerkId: identity.subject,
      });
    }

    // Create dog record
    const dogId = await ctx.runMutation(internal.booking.createDog, {
      name: args.dogName,
      breed: args.dogBreed,
      notes: args.dogNotes,
      ownerId,
    });

    // Enforce intake completion before booking
    const intakeOk = await ctx.runQuery(api.intakes.isIntakeComplete as unknown as any, { ownerId, dogId } as any);
    if (!intakeOk?.ok) {
      throw new Error("Please complete the intake form at /book/intake before booking.");
    }

    // Create all bookings and confirm them immediately (since we're not using payments yet)
    const bookingIds: string[] = [];
    const basePrice = 25;
    const pricePerMinute = 0.5;

    for (const booking of args.bookings) {
      const totalPrice = basePrice + (booking.durationMins * pricePerMinute);
      
      const bookingId: Id<"bookings"> = await ctx.runMutation(internal.booking.createPendingBooking, {
        dogId,
        ownerId,
        startTime: booking.startTime,
        durationMins: booking.durationMins,
        price: totalPrice,
        createdAt: new Date().toISOString(),
      });

      // For now, immediately confirm the booking since we're not using payments
      await ctx.runMutation(internal.booking.confirmBooking, { bookingId });
      
      // Schedule confirmation email
      await ctx.scheduler.runAfter(0, internal.emails.sendBookingConfirmation, {
        bookingId,
      });
      
      bookingIds.push(bookingId);
    }

    return bookingIds;
  },
});

// Creates a booking for an authenticated user
export const bookWalkAuth = action({
  args: {
    startTime: v.string(),
    durationMins: v.number(),
    dogName: v.string(),
    dogBreed: v.optional(v.string()),
    dogNotes: v.optional(v.string()),
  },
  handler: async (ctx, args): Promise<string> => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Must be signed in to book a walk");
    }

    // Check if time slot is still available BEFORE creating anything
    const conflictCheck = await ctx.runQuery(api.booking.checkSlotAvailability, {
      startTime: args.startTime,
      durationMins: args.durationMins,
    });
    
    if (!conflictCheck.available) {
      throw new Error(`This time slot is no longer available. ${conflictCheck.reason}`);
    }

    // Get or create owner record based on the authenticated user (Clerk)
    const existingOwner = await ctx.runQuery(api.owners.getByEmail, {
      email: identity.email!,
    });
    
    let ownerId: Id<"owners">;
    if (existingOwner) {
      ownerId = existingOwner._id;
      
      // Update Clerk ID if it's missing
      if (!existingOwner.clerkId && identity.subject) {
        await ctx.runMutation(internal.booking.updateOwnerClerkId, {
          ownerId,
          clerkId: identity.subject,
        });
      }
    } else {
      // Create owner record from Clerk user
      ownerId = await ctx.runMutation(internal.booking.createOwnerFromClerk, {
        email: identity.email!,
        name: identity.name || "User",
        clerkId: identity.subject,
      });
    }

    // Create or get dog record
    const dogId = await ctx.runMutation(internal.booking.createDog, {
      name: args.dogName,
      breed: args.dogBreed,
      notes: args.dogNotes,
      ownerId,
    });

    // Enforce intake completion before booking
    const intakeOk = await ctx.runQuery(api.intakes.isIntakeComplete as unknown as any, { ownerId, dogId } as any);
    if (!intakeOk?.ok) {
      throw new Error("Please complete the intake form at /book/intake before booking.");
    }

    // Calculate price (basic pricing logic)
    const basePrice = 25; // $25 base price
    const pricePerMinute = 0.5; // $0.50 per minute
    const totalPrice = basePrice + (args.durationMins * pricePerMinute);

    // Create booking with price and creation timestamp
    const bookingId: Id<"bookings"> = await ctx.runMutation(internal.booking.createPendingBooking, {
      dogId,
      ownerId,
      startTime: args.startTime,
      durationMins: args.durationMins,
      price: totalPrice,
      createdAt: new Date().toISOString(),
    });

    // Create placeholder payment URL
    const paymentUrl: string = `https://example.com/pay/${bookingId}`;
    const stripeId: string = `cs_test_${bookingId}`;

    // Update booking with payment info
    await ctx.runMutation(internal.booking.linkStripe, {
      bookingId,
      paymentUrl,
      stripeId,
    });

    return paymentUrl;
  },
});

// Creates a booking and returns a Stripe payment URL
export const bookWalk = action({
  args: {
    dogId: v.id("dogs"),
    ownerId: v.id("owners"),
    startTime: v.string(),
    durationMins: v.number(),
  },
  handler: async (ctx, args): Promise<string> => {
    // 1. Create a "pending" booking record in the database.
    // We use an internal mutation to ensure this can only be called from our action.
    const bookingId: Id<"bookings"> = await ctx.runMutation(internal.booking.createPendingBooking, {
      dogId: args.dogId,
      ownerId: args.ownerId,
      startTime: args.startTime,
      durationMins: args.durationMins,
      price: 25, // Default price for legacy action
      createdAt: new Date().toISOString(),
    });

    // 2. Create a Stripe Checkout Session (placeholder)
    // In a real app, you would use the Stripe Node.js library here.
    // Ensure you have STRIPE_SECRET_KEY in your Convex environment variables.
    const stripeSecretKey = process.env.STRIPE_SECRET_KEY;
    if (!stripeSecretKey) {
      throw new Error("STRIPE_SECRET_KEY environment variable not set.");
    }

    // --- Placeholder for Stripe API call ---
    // const stripe = new Stripe(stripeSecretKey);
    // const session = await stripe.checkout.sessions.create({ ... });
    // const paymentUrl = session.url;
    // const stripeId = session.id;
    //
    // For this example, we'll use a placeholder URL.
    const paymentUrl: string = `https://example.com/pay/${bookingId}`;
    const stripeId: string = `cs_test_${bookingId}`;
    // -----------------------------------------

    // 3. Update the booking with the payment URL and Stripe session ID.
    await ctx.runMutation(internal.booking.linkStripe, {
      bookingId,
      paymentUrl,
      stripeId,
    });

    return paymentUrl;
  },
});

// --- Internal Mutations (only callable from other Convex functions) ---

export const createPendingBooking = internalMutation({
  args: {
    dogId: v.id("dogs"),
    ownerId: v.id("owners"),
    startTime: v.string(),
    durationMins: v.number(),
    price: v.number(),
    createdAt: v.string(),
  },
  handler: async (ctx, args) => {
    // Calculate end time
    const startDate = new Date(args.startTime);
    const endDate = new Date(startDate.getTime() + args.durationMins * 60 * 1000);
    
    return await ctx.db.insert("bookings", {
      ...args,
      endTime: endDate.toISOString(),
      status: "pending",
    });
  },
});

export const linkStripe = internalMutation({
  args: {
    bookingId: v.id("bookings"),
    paymentUrl: v.string(),
    stripeId: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.bookingId, {
      paymentUrl: args.paymentUrl,
      stripeId: args.stripeId,
    });
  },
});

export const createOwnerFromClerk = internalMutation({
  args: {
    email: v.string(),
    name: v.string(),
    clerkId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("owners", {
      name: args.name,
      email: args.email,
      clerkId: args.clerkId,
      // phone can be added later via profile update
    });
  },
});

export const createDog = internalMutation({
  args: {
    name: v.string(),
    breed: v.optional(v.string()),
    notes: v.optional(v.string()),
    ownerId: v.id("owners"),
  },
  handler: async (ctx, args) => {
    return await ctx.db.insert("dogs", args);
  },
});

// Check if a time slot is available (prevent double booking)
export const checkSlotAvailability = query({
  args: {
    startTime: v.string(),
    durationMins: v.number(),
  },
  handler: async (ctx, args) => {
    const startDate = new Date(args.startTime);
    const endDate = new Date(startDate.getTime() + (args.durationMins * 60 * 1000));
    
    // Check for overlapping confirmed bookings
    const overlappingBookings = await ctx.db
      .query("bookings")
      .withIndex("by_status_and_time", (q) => 
        q.eq("status", "confirmed")
         .gte("startTime", startDate.toISOString())
         .lt("startTime", endDate.toISOString())
      )
      .collect();
    
    // Check capacity - max 3 dogs per slot
    const MAX_CAPACITY = 3;
    const currentBookings = overlappingBookings.length;
    
    if (currentBookings >= MAX_CAPACITY) {
      return {
        available: false,
        reason: `Time slot is fully booked (${currentBookings}/${MAX_CAPACITY} dogs scheduled)`,
        currentBookings,
        maxCapacity: MAX_CAPACITY,
      };
    }
    
    return {
      available: true,
      reason: "Slot is available",
      currentBookings,
      maxCapacity: MAX_CAPACITY,
      remainingCapacity: MAX_CAPACITY - currentBookings,
    };
  },
});

// Update owner's Clerk ID
export const updateOwnerClerkId = internalMutation({
  args: {
    ownerId: v.id("owners"),
    clerkId: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.ownerId, {
      clerkId: args.clerkId,
    });
  },
});

// Manual booking confirmation for testing (replaces Stripe webhook)
export const manuallyConfirmBooking = action({
  args: { bookingId: v.id("bookings") },
  handler: async (ctx, { bookingId }) => {
    // Get the booking
    const booking = await ctx.runQuery(api.booking.getBookingById, { bookingId });
    if (!booking) {
      throw new Error("Booking not found");
    }

    if (booking.status === "confirmed") {
      return { success: true, message: "Booking already confirmed" };
    }

    if (booking.status !== "pending") {
      throw new Error(`Cannot confirm booking with status: ${booking.status}`);
    }

    // Update status to confirmed
    await ctx.runMutation(internal.booking.confirmBooking, { bookingId });

    // Schedule the email to be sent immediately
    await ctx.scheduler.runAfter(0, internal.emails.sendBookingConfirmation, {
      bookingId,
    });

    return { success: true, message: "Booking confirmed and email sent!" };
  },
});

// Get booking by ID
export const getBookingById = query({
  args: { bookingId: v.id("bookings") },
  handler: async (ctx, { bookingId }) => {
    return await ctx.db.get(bookingId);
  },
});

// Get current user's bookings with related data
export const getUserBookings = query({
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Must be signed in to view bookings");
    }

    // Find the user's owner record
    const owner = await ctx.db
      .query("owners")
      .withIndex("by_email", (q) => q.eq("email", identity.email!))
      .first();

    if (!owner) {
      return []; // No bookings if user doesn't exist yet
    }

    // Get user's bookings
    const bookings = await ctx.db
      .query("bookings")
      .withIndex("by_owner", (q) => q.eq("ownerId", owner._id))
      .order("desc")
      .collect();

    // Fetch related dog data for each booking
    const bookingsWithDetails = await Promise.all(
      bookings.map(async (booking) => {
        const dog = await ctx.db.get(booking.dogId);
        return {
          ...booking,
          dog,
        };
      })
    );

    return bookingsWithDetails;
  },
});

// Internal mutation to confirm booking
export const confirmBooking = internalMutation({
  args: { bookingId: v.id("bookings") },
  handler: async (ctx, { bookingId }) => {
    await ctx.db.patch(bookingId, { status: "confirmed" });
    try {
      await ctx.scheduler.runAfter(0, internal.calendarSync.createCalendarEvent, { bookingId });
    } catch {}
  },
});

// Internal query to get booking details for emails
export const getBookingDetailsForEmail = internalQuery({
  args: { bookingId: v.id("bookings") },
  handler: async (ctx, { bookingId }) => {
    const booking = await ctx.db.get(bookingId);
    if (!booking) return null;

    const owner = await ctx.db.get(booking.ownerId);
    if (!owner) return null;

    const dog = await ctx.db.get(booking.dogId);
    if (!dog) return null;

    return { booking, owner, dog };
  },
});

// Mark reminder as sent (used by reminders system)
export const markReminderSent = mutation({
  args: { bookingId: v.id("bookings") },
  handler: async (ctx, { bookingId }) => {
    await ctx.db.patch(bookingId, { 
      reminderSent: true 
    });
  },
});

// Cancel a booking (customer action)
export const cancelBooking = action({
  args: { 
    bookingId: v.id("bookings"),
    reason: v.optional(v.string())
  },
  handler: async (ctx, { bookingId, reason }) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Must be signed in to cancel booking");
    }

    // Get the booking and verify ownership
    const booking = await ctx.runQuery(api.booking.getBookingById, { bookingId });
    if (!booking) {
      throw new Error("Booking not found");
    }

    // Get owner to verify this user owns the booking
    const owner = await ctx.runQuery(internal.booking.getOwnerById, { ownerId: booking.ownerId });
    if (!owner || owner.email !== identity.email) {
      throw new Error("You can only cancel your own bookings");
    }

    // Check if booking can be cancelled
    if (booking.status === "cancelled") {
      throw new Error("Booking is already cancelled");
    }

    if (booking.status === "completed") {
      throw new Error("Cannot cancel a completed booking");
    }

    // Check if booking is within cancellation window (24 hours)
    const bookingTime = new Date(booking.startTime);
    const now = new Date();
    const hoursUntilBooking = (bookingTime.getTime() - now.getTime()) / (1000 * 60 * 60);
    
    if (hoursUntilBooking < 24) {
      throw new Error("Bookings can only be cancelled at least 24 hours in advance");
    }

    // Cancel the booking
    await ctx.runMutation(internal.booking.updateBookingStatus, {
      bookingId,
      status: "cancelled",
      cancellationReason: reason,
      cancelledAt: new Date().toISOString()
    });

    // Schedule cancellation email to be sent immediately
    await ctx.scheduler.runAfter(0, internal.emails.sendBookingCancellation, { bookingId });

    // Remove calendar event if present
    if (booking.calendarEventId) {
      try {
        await ctx.runAction(internal.calendarSync.deleteCalendarEvent, { eventId: booking.calendarEventId });
      } catch {}
    }

    return { 
      success: true, 
      message: "Booking cancelled successfully. You'll receive a confirmation email shortly."
    };
  },
});

// Internal query to get owner by ID
export const getOwnerById = internalQuery({
  args: { ownerId: v.id("owners") },
  handler: async (ctx, { ownerId }) => {
    return await ctx.db.get(ownerId);
  },
});

// Internal mutation to update booking status with additional fields
export const updateBookingStatus = internalMutation({
  args: {
    bookingId: v.id("bookings"),
    status: v.union(
      v.literal("pending"),
      v.literal("confirmed"),
      v.literal("completed"),
      v.literal("cancelled")
    ),
    cancellationReason: v.optional(v.string()),
    cancelledAt: v.optional(v.string())
  },
  handler: async (ctx, { bookingId, status, cancellationReason, cancelledAt }) => {
    const updateData: any = { status };
    
    if (cancellationReason) {
      updateData.cancellationReason = cancellationReason;
    }
    
    if (cancelledAt) {
      updateData.cancelledAt = cancelledAt;
    }
    
    await ctx.db.patch(bookingId, updateData);
  },
});
