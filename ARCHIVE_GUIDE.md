# WiggleWalk Archive Guide

This document lists files that have been archived and the reasoning behind each decision.

## Archived Files

### Documentation Files
- `CLERK_SETUP.md` - Setup documentation that's likely outdated
- `SHADCN_MIGRATION_GUIDE.md` - Migration guide that's probably no longer needed
- `VOICE_PLAN.md` - Planning document that's been superseded by implementation
- `PLAN.md` - Original project plan
- `convex/README.md` - Convex-specific documentation
- `LIVEKIT_AGENT_ANALYSIS.md` - Analysis document (kept for reference but archived)
- `ENVIRONMENT_VARIABLES.md` - Environment documentation (reference copy archived)

### Development/Setup Scripts
- `seed-availability.js` - One-time seed script
- `scripts/seed-strapi.mjs` - Content management system seeding
- `scripts/generate-dev-cert.mjs` - Development certificate generation
- `docker/` - Docker configuration (if not currently used)

### Test Files
- `src/components/__tests__/` - Test files (kept for reference but archived)

## Files Kept in Main Project

### Core Application Files
- All `src/` files - Main application code
- All `convex/` files - Backend logic
- `livekit-python-agent/` - Voice agent implementation
- Configuration files (package.json, tsconfig.json, etc.)
- Environment files (.env, .env.local)
- Main application files (index.html, vite.config.ts)

### Active Development Files
- `scripts/livekit-bot.mjs` - Active bot script
- `scripts/ws-bridge.mjs` - WebSocket bridge
- `scripts/generate-bot-token.mjs` - Token generation

## Archive Process

Files were moved to the `archive/` directory to keep the main project clean while preserving historical documentation and one-time scripts.

## Restoration

To restore any archived file:
```bash
# Copy specific file
cp archive/FILENAME DESTINATION

# Or restore entire directory
cp -r archive/DIRECTORY DESTINATION
