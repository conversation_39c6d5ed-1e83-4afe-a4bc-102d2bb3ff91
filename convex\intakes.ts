import { mutation, query } from "./_generated/server";
import type { Id } from "./_generated/dataModel";
import { v } from "convex/values";

export const create = mutation({
  args: {
    owner: v.object({ fullName: v.string(), email: v.string(), phone: v.optional(v.string()), neighborhood: v.optional(v.string()) }),
    dog: v.object({ name: v.string(), age: v.optional(v.number()), breed: v.optional(v.string()), weight: v.optional(v.number()) }),
    prerequisites: v.object({ vaccines: v.boolean(), license: v.boolean(), friendly: v.boolean() }),
    questionnaire: v.optional(v.object({
      temperament: v.optional(v.array(v.string())),
      recall: v.optional(v.string()),
      carRide: v.optional(v.string()),
      leash: v.optional(v.string()),
      triggers: v.optional(v.array(v.string())),
      healthNeeds: v.optional(v.string()),
      schedulePref: v.optional(v.object({ days: v.array(v.string()), windows: v.array(v.string()) })),
    })),
    introPreferredSlots: v.optional(v.array(v.string())),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Upsert owner by email
    let owner = await ctx.db.query("owners").withIndex("by_email", (q) => q.eq("email", args.owner.email)).first();
    if (!owner) {
      const ownerId = await ctx.db.insert("owners", { name: args.owner.fullName, email: args.owner.email, phone: args.owner.phone });
      owner = await ctx.db.get(ownerId);
    }

    // Create dog
    const dogId = await ctx.db.insert("dogs", {
      ownerId: owner!._id,
      name: args.dog.name,
      breed: args.dog.breed,
      age: args.dog.age,
      weight: args.dog.weight,
    });

    // Create intake
    const intakeId = await ctx.db.insert("intakes", {
      ownerId: owner!._id,
      dogId,
      prerequisites: args.prerequisites,
      questionnaire: args.questionnaire,
      introCall: { preferredSlots: args.introPreferredSlots ?? [] },
      status: "intake_submitted",
      notes: args.notes,
      history: [{ at: Date.now(), action: "created" }],
      createdAt: Date.now(),
    });

    return { intakeId };
  },
});

export const list = query({
  args: {},
  handler: async (ctx) => {
    const intakes = await ctx.db.query("intakes").collect();
    // Join minimal owner/dog info
    return await Promise.all(intakes.map(async (i) => {
      const owner = await ctx.db.get(i.ownerId);
      const dog = await ctx.db.get(i.dogId);
      return {
        _id: i._id,
        ownerName: owner?.name,
        ownerEmail: owner?.email,
        dogName: dog?.name,
        dogAge: dog?.age,
        status: i.status,
        schedulePref: i.questionnaire?.schedulePref ?? { days: [], windows: [] },
        preferredSlots: i.introCall?.preferredSlots ?? [],
        createdAt: i.createdAt,
      };
    }));
  },
});

// Upsert partial intake info coming from the voice agent
export const upsertFromAgent = mutation({
  args: {
    ownerEmail: v.string(),
    ownerName: v.optional(v.string()),
    dog: v.optional(v.object({ name: v.string(), breed: v.optional(v.string()) })),
    questionnaire: v.optional(v.object({
      temperament: v.optional(v.array(v.string())),
      recall: v.optional(v.string()),
      carRide: v.optional(v.string()),
      leash: v.optional(v.string()),
      triggers: v.optional(v.array(v.string())),
      healthNeeds: v.optional(v.string()),
      schedulePref: v.optional(v.object({ days: v.array(v.string()), windows: v.array(v.string()) })),
    })),
    prerequisites: v.optional(v.object({ vaccines: v.boolean(), license: v.boolean(), friendly: v.boolean() })),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Ensure owner
    let owner = await ctx.db.query("owners").withIndex("by_email", (q) => q.eq("email", args.ownerEmail)).first();
    if (!owner) {
      const ownerId = await ctx.db.insert("owners", { name: args.ownerName || "User", email: args.ownerEmail });
      owner = await ctx.db.get(ownerId);
    }
    // Ensure dog
    let dogId = undefined as undefined | string;
    if (args.dog?.name) {
      const dogs = await ctx.db.query("dogs").withIndex("by_owner", (q) => q.eq("ownerId", owner!._id)).collect();
      const existing = dogs.find((d) => d.name?.toLowerCase?.() === args.dog!.name.toLowerCase());
      dogId = existing?._id || (await ctx.db.insert("dogs", { ownerId: owner!._id, name: args.dog!.name, breed: args.dog?.breed }));
    } else {
      const oneDog = await ctx.db.query("dogs").withIndex("by_owner", (q) => q.eq("ownerId", owner!._id)).first();
      dogId = oneDog?._id;
    }
    if (!dogId) {
      // No dog info yet; store owner-level intake shell
      return { ownerId: owner!._id };
    }
    // Upsert intake for owner+dog
    let intake = await ctx.db
      .query("intakes")
      .withIndex("by_owner", (q) => q.eq("ownerId", owner!._id))
      .filter((q) => q.eq(q.field("dogId"), dogId!))
      .order("desc")
      .first();
    if (!intake) {
      const id = await ctx.db.insert("intakes", {
        ownerId: owner!._id,
        dogId: dogId as Id<"dogs">,
        prerequisites: args.prerequisites || { vaccines: false, license: false, friendly: false },
        questionnaire: args.questionnaire,
        introCall: { preferredSlots: [] },
        status: "intake_submitted",
        notes: args.notes,
        history: [{ at: Date.now(), action: "created" }],
        createdAt: Date.now(),
      });
      intake = await ctx.db.get(id);
    } else {
      const patch: Record<string, unknown> = {};
      if (args.prerequisites) patch.prerequisites = args.prerequisites;
      if (args.questionnaire) patch.questionnaire = { ...(intake.questionnaire || {}), ...args.questionnaire } as Record<string, unknown>;
      if (args.notes) patch.notes = args.notes;
      if (Object.keys(patch).length) await ctx.db.patch(intake._id, patch);
    }
    return { ownerId: owner!._id, dogId };
  },
});

export const getLatestForOwner = query({
  args: { ownerId: v.id("owners") },
  handler: async (ctx, { ownerId }) => {
    return await ctx.db
      .query("intakes")
      .withIndex("by_owner", (q) => q.eq("ownerId", ownerId))
      .order("desc")
      .first();
  },
});

export const isIntakeComplete = query({
  args: { ownerId: v.id("owners"), dogId: v.id("dogs") },
  handler: async (ctx, { ownerId, dogId }) => {
    const intake = await ctx.db
      .query("intakes")
      .withIndex("by_owner", (q) => q.eq("ownerId", ownerId))
      .filter((q) => q.eq(q.field("dogId"), dogId))
      .order("desc")
      .first();
    if (!intake) return { ok: false, reason: "no_intake" } as const;
    const p = intake.prerequisites || { vaccines: false, license: false, friendly: false };
    const ok = !!(p.vaccines && p.license && p.friendly);
    return (ok
      ? { ok: true as const }
      : { ok: false as const, reason: "missing_prerequisites" as const });
  },
});