"use node";
import { action } from "./_generated/server";
import { v } from "convex/values";

export const issueToken = action({
  args: {
    subject: v.string(),
    name: v.optional(v.string()),
  },
  handler: async (_ctx, { subject, name }): Promise<{ url: string; token: string; roomName: string }> => {
    const API_KEY = process.env.LIVEKIT_API_KEY;
    const API_SECRET = process.env.LIVEKIT_API_SECRET;
    const LIVEKIT_URL = process.env.LIVEKIT_URL as string | undefined;
    if (!API_KEY || !API_SECRET || !LIVEKIT_URL) {
      throw new Error("livekit_not_configured");
    }

    const participantIdentity = subject;
    const participantName = name || "User";
    // Generate a unique, ephemeral room per session for privacy
    const shortSub = (subject || "user").split(":").pop() || "user";
    const nonce = Math.random().toString(36).slice(2, 8);
    const roomName = `wigglewalk-${shortSub}-${nonce}-${Date.now().toString(36)}`;

    // Dynamic import to avoid build-time type resolution issues
    const { AccessToken } = (await import("livekit-server-sdk")) as unknown as {
      AccessToken: new (apiKey: string, apiSecret: string, opts?: { identity?: string; name?: string }) => {
        addGrant: (g: unknown) => void;
        toJwt: () => Promise<string>;
      };
    };
    const at = new AccessToken(API_KEY, API_SECRET, {
      identity: participantIdentity,
      name: participantName,
    });
    at.addGrant({
      room: roomName,
      roomJoin: true,
      canPublish: true,
      canPublishData: true,
      canSubscribe: true,
    });
    const token = await at.toJwt();
    return { url: LIVEKIT_URL, token, roomName };
  },
});
